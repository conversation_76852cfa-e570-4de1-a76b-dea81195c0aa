<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.homework.assistant.shared.utils.IdGeneratorTest" tests="9" skipped="0" failures="0" errors="0" timestamp="2025-07-02T15:09:09" hostname="zxnapdeMacBook-Pro.local" time="0.001">
  <properties/>
  <testcase name="生成的ID应该有正确的长度" classname="com.homework.assistant.shared.utils.IdGeneratorTest" time="0.0"/>
  <testcase name="带前缀的ID应该包含正确的前缀" classname="com.homework.assistant.shared.utils.IdGeneratorTest" time="0.0"/>
  <testcase name="学习会话ID应该有正确的前缀" classname="com.homework.assistant.shared.utils.IdGeneratorTest" time="0.0"/>
  <testcase name="背诵ID应该有正确的前缀" classname="com.homework.assistant.shared.utils.IdGeneratorTest" time="0.0"/>
  <testcase name="生成的ID应该有指定的长度" classname="com.homework.assistant.shared.utils.IdGeneratorTest" time="0.0"/>
  <testcase name="生字ID应该有正确的前缀" classname="com.homework.assistant.shared.utils.IdGeneratorTest" time="0.0"/>
  <testcase name="连续生成的ID应该不相同" classname="com.homework.assistant.shared.utils.IdGeneratorTest" time="0.0"/>
  <testcase name="生成的ID应该只包含允许的字符" classname="com.homework.assistant.shared.utils.IdGeneratorTest" time="0.001"/>
  <testcase name="分类ID应该有正确的前缀" classname="com.homework.assistant.shared.utils.IdGeneratorTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
