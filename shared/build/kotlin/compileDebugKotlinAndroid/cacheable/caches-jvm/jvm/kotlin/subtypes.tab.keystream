kotlin.Enum8com.homework.assistant.shared.ai.SemanticAnalysisService2kotlinx.serialization.internal.GeneratedSerializer8com.homework.assistant.shared.data.local.LocalDataSourceBcom.homework.assistant.shared.data.repository.RecitationRepositoryDcom.homework.assistant.shared.data.repository.StudySessionRepository<com.homework.assistant.shared.data.repository.WordRepository8com.homework.assistant.shared.dictation.DictationService:com.homework.assistant.shared.recitation.RecitationService7com.homework.assistant.shared.voice.VoiceCommandService&com.homework.assistant.shared.Platform7com.homework.assistant.shared.voice.TextToSpeechService;com.homework.assistant.shared.voice.VoiceRecognitionService                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   