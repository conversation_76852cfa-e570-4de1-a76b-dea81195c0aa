  SDK_INT android.os.Build.VERSION  AndroidPlatform com.homework.assistant.shared  Platform com.homework.assistant.shared  String com.homework.assistant.shared  android com.homework.assistant.shared  getPlatform com.homework.assistant.shared  android -com.homework.assistant.shared.AndroidPlatform  Boolean  com.homework.assistant.shared.ai  Float  com.homework.assistant.shared.ai  HintType  com.homework.assistant.shared.ai  Int  com.homework.assistant.shared.ai  IntentRecognitionResult  com.homework.assistant.shared.ai  List  com.homework.assistant.shared.ai  SemanticAnalysisResult  com.homework.assistant.shared.ai  SemanticAnalysisService  com.homework.assistant.shared.ai  String  com.homework.assistant.shared.ai  TextSimilarity  com.homework.assistant.shared.ai  CONTEXT )com.homework.assistant.shared.ai.HintType  MEANING )com.homework.assistant.shared.ai.HintType  
NEXT_SENTENCE )com.homework.assistant.shared.ai.HintType  	NEXT_WORD )com.homework.assistant.shared.ai.HintType  RHYME )com.homework.assistant.shared.ai.HintType  HintType 8com.homework.assistant.shared.ai.SemanticAnalysisService  calculateSimilarity 8com.homework.assistant.shared.ai.SemanticAnalysisService  Boolean %com.homework.assistant.shared.ai.impl  Float %com.homework.assistant.shared.ai.impl  HintType %com.homework.assistant.shared.ai.impl  Int %com.homework.assistant.shared.ai.impl  Intent %com.homework.assistant.shared.ai.impl  IntentRecognitionResult %com.homework.assistant.shared.ai.impl  List %com.homework.assistant.shared.ai.impl  SemanticAnalysisResult %com.homework.assistant.shared.ai.impl  SemanticAnalysisService %com.homework.assistant.shared.ai.impl  	Sentiment %com.homework.assistant.shared.ai.impl  SimilarityDetails %com.homework.assistant.shared.ai.impl  SimilarityMethod %com.homework.assistant.shared.ai.impl  String %com.homework.assistant.shared.ai.impl  TextSimilarity %com.homework.assistant.shared.ai.impl  contains %com.homework.assistant.shared.ai.impl  	emptyList %com.homework.assistant.shared.ai.impl  isEmpty %com.homework.assistant.shared.ai.impl  listOf %com.homework.assistant.shared.ai.impl  take %com.homework.assistant.shared.ai.impl  trim %com.homework.assistant.shared.ai.impl  HintType Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  Intent Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  IntentRecognitionResult Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  SemanticAnalysisResult Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  	Sentiment Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  SimilarityDetails Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  SimilarityMethod Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  TextSimilarity Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  contains Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  	emptyList Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  isEmpty Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  listOf Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  take Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  trim Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  Boolean &com.homework.assistant.shared.ai.model  Entity &com.homework.assistant.shared.ai.model  
EntityType &com.homework.assistant.shared.ai.model  Float &com.homework.assistant.shared.ai.model  HintType &com.homework.assistant.shared.ai.model  Int &com.homework.assistant.shared.ai.model  Intent &com.homework.assistant.shared.ai.model  IntentRecognitionResult &com.homework.assistant.shared.ai.model  List &com.homework.assistant.shared.ai.model  Long &com.homework.assistant.shared.ai.model  Map &com.homework.assistant.shared.ai.model  SemanticAnalysisResult &com.homework.assistant.shared.ai.model  SemanticAnalysisService &com.homework.assistant.shared.ai.model  	Sentiment &com.homework.assistant.shared.ai.model  Serializable &com.homework.assistant.shared.ai.model  SimilarityDetails &com.homework.assistant.shared.ai.model  SimilarityMethod &com.homework.assistant.shared.ai.model  String &com.homework.assistant.shared.ai.model  TextSimilarity &com.homework.assistant.shared.ai.model  contains &com.homework.assistant.shared.ai.model  	emptyList &com.homework.assistant.shared.ai.model  emptyMap &com.homework.assistant.shared.ai.model  isEmpty &com.homework.assistant.shared.ai.model  listOf &com.homework.assistant.shared.ai.model  take &com.homework.assistant.shared.ai.model  trim &com.homework.assistant.shared.ai.model  
EntityType -com.homework.assistant.shared.ai.model.Entity  Float -com.homework.assistant.shared.ai.model.Entity  Int -com.homework.assistant.shared.ai.model.Entity  String -com.homework.assistant.shared.ai.model.Entity  ASK_QUESTION -com.homework.assistant.shared.ai.model.Intent  
CANCEL_ACTION -com.homework.assistant.shared.ai.model.Intent  CONFIRM_ACTION -com.homework.assistant.shared.ai.model.Intent  	Companion -com.homework.assistant.shared.ai.model.Intent  REQUEST_HELP -com.homework.assistant.shared.ai.model.Intent  REQUEST_HINT -com.homework.assistant.shared.ai.model.Intent  START_DICTATION -com.homework.assistant.shared.ai.model.Intent  START_RECITATION -com.homework.assistant.shared.ai.model.Intent  UNKNOWN -com.homework.assistant.shared.ai.model.Intent  Float >com.homework.assistant.shared.ai.model.IntentRecognitionResult  Intent >com.homework.assistant.shared.ai.model.IntentRecognitionResult  List >com.homework.assistant.shared.ai.model.IntentRecognitionResult  Map >com.homework.assistant.shared.ai.model.IntentRecognitionResult  String >com.homework.assistant.shared.ai.model.IntentRecognitionResult  	emptyList >com.homework.assistant.shared.ai.model.IntentRecognitionResult  emptyMap >com.homework.assistant.shared.ai.model.IntentRecognitionResult  	emptyList Hcom.homework.assistant.shared.ai.model.IntentRecognitionResult.Companion  emptyMap Hcom.homework.assistant.shared.ai.model.IntentRecognitionResult.Companion  Entity =com.homework.assistant.shared.ai.model.SemanticAnalysisResult  Float =com.homework.assistant.shared.ai.model.SemanticAnalysisResult  List =com.homework.assistant.shared.ai.model.SemanticAnalysisResult  Long =com.homework.assistant.shared.ai.model.SemanticAnalysisResult  	Sentiment =com.homework.assistant.shared.ai.model.SemanticAnalysisResult  String =com.homework.assistant.shared.ai.model.SemanticAnalysisResult  	Companion 0com.homework.assistant.shared.ai.model.Sentiment  POSITIVE 0com.homework.assistant.shared.ai.model.Sentiment  Int 8com.homework.assistant.shared.ai.model.SimilarityDetails  List 8com.homework.assistant.shared.ai.model.SimilarityDetails  String 8com.homework.assistant.shared.ai.model.SimilarityDetails  	Companion 7com.homework.assistant.shared.ai.model.SimilarityMethod  SEMANTIC 7com.homework.assistant.shared.ai.model.SimilarityMethod  Float 5com.homework.assistant.shared.ai.model.TextSimilarity  SimilarityDetails 5com.homework.assistant.shared.ai.model.TextSimilarity  SimilarityMethod 5com.homework.assistant.shared.ai.model.TextSimilarity  String 5com.homework.assistant.shared.ai.model.TextSimilarity  
similarity 5com.homework.assistant.shared.ai.model.TextSimilarity  Flow (com.homework.assistant.shared.data.local  Int (com.homework.assistant.shared.data.local  List (com.homework.assistant.shared.data.local  LocalDataSource (com.homework.assistant.shared.data.local  Long (com.homework.assistant.shared.data.local  
Recitation (com.homework.assistant.shared.data.local  RecitationCategory (com.homework.assistant.shared.data.local  RecitationType (com.homework.assistant.shared.data.local  String (com.homework.assistant.shared.data.local  StudySession (com.homework.assistant.shared.data.local  	StudyType (com.homework.assistant.shared.data.local  Word (com.homework.assistant.shared.data.local  WordCategory (com.homework.assistant.shared.data.local  cleanupOldStudySessions 8com.homework.assistant.shared.data.local.LocalDataSource  deleteRecitation 8com.homework.assistant.shared.data.local.LocalDataSource  deleteRecitationCategory 8com.homework.assistant.shared.data.local.LocalDataSource  deleteStudySession 8com.homework.assistant.shared.data.local.LocalDataSource  
deleteWord 8com.homework.assistant.shared.data.local.LocalDataSource  deleteWordCategory 8com.homework.assistant.shared.data.local.LocalDataSource  getAllRecitationCategories 8com.homework.assistant.shared.data.local.LocalDataSource  getAllRecitations 8com.homework.assistant.shared.data.local.LocalDataSource  getAllStudySessions 8com.homework.assistant.shared.data.local.LocalDataSource  getAllWordCategories 8com.homework.assistant.shared.data.local.LocalDataSource  getAllWords 8com.homework.assistant.shared.data.local.LocalDataSource  getRecitationById 8com.homework.assistant.shared.data.local.LocalDataSource  getRecitationCountByCategory 8com.homework.assistant.shared.data.local.LocalDataSource  getRecitationsByCategory 8com.homework.assistant.shared.data.local.LocalDataSource  getRecitationsByType 8com.homework.assistant.shared.data.local.LocalDataSource  getStudySessionById 8com.homework.assistant.shared.data.local.LocalDataSource  getStudySessionsByContentId 8com.homework.assistant.shared.data.local.LocalDataSource  getStudySessionsByDateRange 8com.homework.assistant.shared.data.local.LocalDataSource  getStudySessionsByType 8com.homework.assistant.shared.data.local.LocalDataSource  getWordById 8com.homework.assistant.shared.data.local.LocalDataSource  getWordCountByCategory 8com.homework.assistant.shared.data.local.LocalDataSource  getWordsByCategory 8com.homework.assistant.shared.data.local.LocalDataSource  insertRecitation 8com.homework.assistant.shared.data.local.LocalDataSource  insertRecitationCategory 8com.homework.assistant.shared.data.local.LocalDataSource  insertRecitations 8com.homework.assistant.shared.data.local.LocalDataSource  insertStudySession 8com.homework.assistant.shared.data.local.LocalDataSource  
insertWord 8com.homework.assistant.shared.data.local.LocalDataSource  insertWordCategory 8com.homework.assistant.shared.data.local.LocalDataSource  insertWords 8com.homework.assistant.shared.data.local.LocalDataSource  searchRecitations 8com.homework.assistant.shared.data.local.LocalDataSource  searchWords 8com.homework.assistant.shared.data.local.LocalDataSource  updateRecitation 8com.homework.assistant.shared.data.local.LocalDataSource  updateRecitationCategory 8com.homework.assistant.shared.data.local.LocalDataSource  updateStudySession 8com.homework.assistant.shared.data.local.LocalDataSource  
updateWord 8com.homework.assistant.shared.data.local.LocalDataSource  updateWordCategory 8com.homework.assistant.shared.data.local.LocalDataSource  Flow -com.homework.assistant.shared.data.local.impl  Int -com.homework.assistant.shared.data.local.impl  List -com.homework.assistant.shared.data.local.impl  LocalDataSource -com.homework.assistant.shared.data.local.impl  Long -com.homework.assistant.shared.data.local.impl  MutableStateFlow -com.homework.assistant.shared.data.local.impl  
Recitation -com.homework.assistant.shared.data.local.impl  RecitationCategory -com.homework.assistant.shared.data.local.impl  RecitationType -com.homework.assistant.shared.data.local.impl  String -com.homework.assistant.shared.data.local.impl  StudySession -com.homework.assistant.shared.data.local.impl  	StudyType -com.homework.assistant.shared.data.local.impl  Word -com.homework.assistant.shared.data.local.impl  WordCategory -com.homework.assistant.shared.data.local.impl  contains -com.homework.assistant.shared.data.local.impl  count -com.homework.assistant.shared.data.local.impl  filter -com.homework.assistant.shared.data.local.impl  forEach -com.homework.assistant.shared.data.local.impl  map -com.homework.assistant.shared.data.local.impl  mutableMapOf -com.homework.assistant.shared.data.local.impl  set -com.homework.assistant.shared.data.local.impl  toList -com.homework.assistant.shared.data.local.impl  MutableStateFlow Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  _recitationCategoriesFlow Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  _recitationsFlow Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  _studySessionsFlow Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  _wordCategoriesFlow Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  
_wordsFlow Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  contains Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  count Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  filter Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  map Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  mutableMapOf Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  recitationCategories Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  recitations Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  set Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  
studySessions Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  toList Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  wordCategories Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  words Acom.homework.assistant.shared.data.local.impl.LocalDataSourceImpl  AppSettings (com.homework.assistant.shared.data.model  Boolean (com.homework.assistant.shared.data.model  
DailyProgress (com.homework.assistant.shared.data.model  DictationSettings (com.homework.assistant.shared.data.model  Double (com.homework.assistant.shared.data.model  Float (com.homework.assistant.shared.data.model  Flow (com.homework.assistant.shared.data.model  GeneralSettings (com.homework.assistant.shared.data.model  Int (com.homework.assistant.shared.data.model  List (com.homework.assistant.shared.data.model  LocalDataSource (com.homework.assistant.shared.data.model  Long (com.homework.assistant.shared.data.model  MutableStateFlow (com.homework.assistant.shared.data.model  PrivacySettings (com.homework.assistant.shared.data.model  
Recitation (com.homework.assistant.shared.data.model  RecitationCategory (com.homework.assistant.shared.data.model  RecitationSettings (com.homework.assistant.shared.data.model  RecitationType (com.homework.assistant.shared.data.model  Result (com.homework.assistant.shared.data.model  Serializable (com.homework.assistant.shared.data.model  String (com.homework.assistant.shared.data.model  StudySession (com.homework.assistant.shared.data.model  StudyStatistics (com.homework.assistant.shared.data.model  StudyStatus (com.homework.assistant.shared.data.model  	StudyType (com.homework.assistant.shared.data.model  StudyTypeStats (com.homework.assistant.shared.data.model  System (com.homework.assistant.shared.data.model  
UISettings (com.homework.assistant.shared.data.model  Unit (com.homework.assistant.shared.data.model  
VoiceSettings (com.homework.assistant.shared.data.model  Word (com.homework.assistant.shared.data.model  WordCategory (com.homework.assistant.shared.data.model  contains (com.homework.assistant.shared.data.model  count (com.homework.assistant.shared.data.model  	emptyList (com.homework.assistant.shared.data.model  filter (com.homework.assistant.shared.data.model  forEach (com.homework.assistant.shared.data.model  map (com.homework.assistant.shared.data.model  mutableMapOf (com.homework.assistant.shared.data.model  set (com.homework.assistant.shared.data.model  toList (com.homework.assistant.shared.data.model  DictationSettings 4com.homework.assistant.shared.data.model.AppSettings  GeneralSettings 4com.homework.assistant.shared.data.model.AppSettings  Long 4com.homework.assistant.shared.data.model.AppSettings  PrivacySettings 4com.homework.assistant.shared.data.model.AppSettings  RecitationSettings 4com.homework.assistant.shared.data.model.AppSettings  System 4com.homework.assistant.shared.data.model.AppSettings  
UISettings 4com.homework.assistant.shared.data.model.AppSettings  
VoiceSettings 4com.homework.assistant.shared.data.model.AppSettings  DictationSettings >com.homework.assistant.shared.data.model.AppSettings.Companion  GeneralSettings >com.homework.assistant.shared.data.model.AppSettings.Companion  PrivacySettings >com.homework.assistant.shared.data.model.AppSettings.Companion  RecitationSettings >com.homework.assistant.shared.data.model.AppSettings.Companion  System >com.homework.assistant.shared.data.model.AppSettings.Companion  
UISettings >com.homework.assistant.shared.data.model.AppSettings.Companion  
VoiceSettings >com.homework.assistant.shared.data.model.AppSettings.Companion  Double 6com.homework.assistant.shared.data.model.DailyProgress  Int 6com.homework.assistant.shared.data.model.DailyProgress  Long 6com.homework.assistant.shared.data.model.DailyProgress  String 6com.homework.assistant.shared.data.model.DailyProgress  Boolean :com.homework.assistant.shared.data.model.DictationSettings  Float :com.homework.assistant.shared.data.model.DictationSettings  Int :com.homework.assistant.shared.data.model.DictationSettings  Long :com.homework.assistant.shared.data.model.DictationSettings  Boolean 8com.homework.assistant.shared.data.model.GeneralSettings  String 8com.homework.assistant.shared.data.model.GeneralSettings  Boolean 8com.homework.assistant.shared.data.model.PrivacySettings  Int 3com.homework.assistant.shared.data.model.Recitation  List 3com.homework.assistant.shared.data.model.Recitation  Long 3com.homework.assistant.shared.data.model.Recitation  String 3com.homework.assistant.shared.data.model.Recitation  System 3com.homework.assistant.shared.data.model.Recitation  author 3com.homework.assistant.shared.data.model.Recitation  category 3com.homework.assistant.shared.data.model.Recitation  content 3com.homework.assistant.shared.data.model.Recitation  	emptyList 3com.homework.assistant.shared.data.model.Recitation  id 3com.homework.assistant.shared.data.model.Recitation  title 3com.homework.assistant.shared.data.model.Recitation  System =com.homework.assistant.shared.data.model.Recitation.Companion  	emptyList =com.homework.assistant.shared.data.model.Recitation.Companion  Int ;com.homework.assistant.shared.data.model.RecitationCategory  Long ;com.homework.assistant.shared.data.model.RecitationCategory  RecitationType ;com.homework.assistant.shared.data.model.RecitationCategory  String ;com.homework.assistant.shared.data.model.RecitationCategory  System ;com.homework.assistant.shared.data.model.RecitationCategory  id ;com.homework.assistant.shared.data.model.RecitationCategory  RecitationType Ecom.homework.assistant.shared.data.model.RecitationCategory.Companion  System Ecom.homework.assistant.shared.data.model.RecitationCategory.Companion  Boolean ;com.homework.assistant.shared.data.model.RecitationSettings  Long ;com.homework.assistant.shared.data.model.RecitationSettings  	Companion 7com.homework.assistant.shared.data.model.RecitationType  POEM 7com.homework.assistant.shared.data.model.RecitationType  Int 5com.homework.assistant.shared.data.model.StudySession  List 5com.homework.assistant.shared.data.model.StudySession  Long 5com.homework.assistant.shared.data.model.StudySession  String 5com.homework.assistant.shared.data.model.StudySession  StudyStatus 5com.homework.assistant.shared.data.model.StudySession  	StudyType 5com.homework.assistant.shared.data.model.StudySession  System 5com.homework.assistant.shared.data.model.StudySession  	contentId 5com.homework.assistant.shared.data.model.StudySession  	emptyList 5com.homework.assistant.shared.data.model.StudySession  id 5com.homework.assistant.shared.data.model.StudySession  	startTime 5com.homework.assistant.shared.data.model.StudySession  type 5com.homework.assistant.shared.data.model.StudySession  StudyStatus ?com.homework.assistant.shared.data.model.StudySession.Companion  System ?com.homework.assistant.shared.data.model.StudySession.Companion  	emptyList ?com.homework.assistant.shared.data.model.StudySession.Companion  
DailyProgress 8com.homework.assistant.shared.data.model.StudyStatistics  Double 8com.homework.assistant.shared.data.model.StudyStatistics  Int 8com.homework.assistant.shared.data.model.StudyStatistics  List 8com.homework.assistant.shared.data.model.StudyStatistics  Long 8com.homework.assistant.shared.data.model.StudyStatistics  StudyTypeStats 8com.homework.assistant.shared.data.model.StudyStatistics  	emptyList 8com.homework.assistant.shared.data.model.StudyStatistics  StudyTypeStats Bcom.homework.assistant.shared.data.model.StudyStatistics.Companion  	emptyList Bcom.homework.assistant.shared.data.model.StudyStatistics.Companion  	COMPLETED 4com.homework.assistant.shared.data.model.StudyStatus  	Companion 4com.homework.assistant.shared.data.model.StudyStatus  IN_PROGRESS 4com.homework.assistant.shared.data.model.StudyStatus  	Companion 2com.homework.assistant.shared.data.model.StudyType  	DICTATION 2com.homework.assistant.shared.data.model.StudyType  
RECITATION 2com.homework.assistant.shared.data.model.StudyType  Double 7com.homework.assistant.shared.data.model.StudyTypeStats  Int 7com.homework.assistant.shared.data.model.StudyTypeStats  Long 7com.homework.assistant.shared.data.model.StudyTypeStats  Boolean 3com.homework.assistant.shared.data.model.UISettings  Float 3com.homework.assistant.shared.data.model.UISettings  String 3com.homework.assistant.shared.data.model.UISettings  Boolean 6com.homework.assistant.shared.data.model.VoiceSettings  Float 6com.homework.assistant.shared.data.model.VoiceSettings  Long 6com.homework.assistant.shared.data.model.VoiceSettings  String 6com.homework.assistant.shared.data.model.VoiceSettings  Int -com.homework.assistant.shared.data.model.Word  List -com.homework.assistant.shared.data.model.Word  Long -com.homework.assistant.shared.data.model.Word  String -com.homework.assistant.shared.data.model.Word  System -com.homework.assistant.shared.data.model.Word  category -com.homework.assistant.shared.data.model.Word  	character -com.homework.assistant.shared.data.model.Word  	emptyList -com.homework.assistant.shared.data.model.Word  id -com.homework.assistant.shared.data.model.Word  meaning -com.homework.assistant.shared.data.model.Word  pinyin -com.homework.assistant.shared.data.model.Word  System 7com.homework.assistant.shared.data.model.Word.Companion  	emptyList 7com.homework.assistant.shared.data.model.Word.Companion  Int 5com.homework.assistant.shared.data.model.WordCategory  Long 5com.homework.assistant.shared.data.model.WordCategory  String 5com.homework.assistant.shared.data.model.WordCategory  System 5com.homework.assistant.shared.data.model.WordCategory  id 5com.homework.assistant.shared.data.model.WordCategory  System ?com.homework.assistant.shared.data.model.WordCategory.Companion  AppSettings -com.homework.assistant.shared.data.repository  
DailyProgress -com.homework.assistant.shared.data.repository  DictationSettings -com.homework.assistant.shared.data.repository  Double -com.homework.assistant.shared.data.repository  Flow -com.homework.assistant.shared.data.repository  GeneralSettings -com.homework.assistant.shared.data.repository  Int -com.homework.assistant.shared.data.repository  List -com.homework.assistant.shared.data.repository  Long -com.homework.assistant.shared.data.repository  PrivacySettings -com.homework.assistant.shared.data.repository  
Recitation -com.homework.assistant.shared.data.repository  RecitationCategory -com.homework.assistant.shared.data.repository  RecitationRepository -com.homework.assistant.shared.data.repository  RecitationSettings -com.homework.assistant.shared.data.repository  RecitationType -com.homework.assistant.shared.data.repository  Result -com.homework.assistant.shared.data.repository  String -com.homework.assistant.shared.data.repository  StudySession -com.homework.assistant.shared.data.repository  StudySessionRepository -com.homework.assistant.shared.data.repository  StudyStatistics -com.homework.assistant.shared.data.repository  	StudyType -com.homework.assistant.shared.data.repository  
UISettings -com.homework.assistant.shared.data.repository  Unit -com.homework.assistant.shared.data.repository  
VoiceSettings -com.homework.assistant.shared.data.repository  Word -com.homework.assistant.shared.data.repository  WordCategory -com.homework.assistant.shared.data.repository  WordRepository -com.homework.assistant.shared.data.repository  
DailyProgress 2com.homework.assistant.shared.data.repository.impl  Double 2com.homework.assistant.shared.data.repository.impl  	Exception 2com.homework.assistant.shared.data.repository.impl  Flow 2com.homework.assistant.shared.data.repository.impl  Int 2com.homework.assistant.shared.data.repository.impl  List 2com.homework.assistant.shared.data.repository.impl  LocalDataSource 2com.homework.assistant.shared.data.repository.impl  Long 2com.homework.assistant.shared.data.repository.impl  
Recitation 2com.homework.assistant.shared.data.repository.impl  RecitationCategory 2com.homework.assistant.shared.data.repository.impl  RecitationRepository 2com.homework.assistant.shared.data.repository.impl  RecitationType 2com.homework.assistant.shared.data.repository.impl  Result 2com.homework.assistant.shared.data.repository.impl  String 2com.homework.assistant.shared.data.repository.impl  StudySession 2com.homework.assistant.shared.data.repository.impl  StudySessionRepository 2com.homework.assistant.shared.data.repository.impl  StudyStatistics 2com.homework.assistant.shared.data.repository.impl  	StudyType 2com.homework.assistant.shared.data.repository.impl  Unit 2com.homework.assistant.shared.data.repository.impl  Word 2com.homework.assistant.shared.data.repository.impl  WordCategory 2com.homework.assistant.shared.data.repository.impl  WordRepository 2com.homework.assistant.shared.data.repository.impl  	emptyList 2com.homework.assistant.shared.data.repository.impl  failure 2com.homework.assistant.shared.data.repository.impl  success 2com.homework.assistant.shared.data.repository.impl  Result Kcom.homework.assistant.shared.data.repository.impl.RecitationRepositoryImpl  Unit Kcom.homework.assistant.shared.data.repository.impl.RecitationRepositoryImpl  failure Kcom.homework.assistant.shared.data.repository.impl.RecitationRepositoryImpl  localDataSource Kcom.homework.assistant.shared.data.repository.impl.RecitationRepositoryImpl  success Kcom.homework.assistant.shared.data.repository.impl.RecitationRepositoryImpl  
DailyProgress Mcom.homework.assistant.shared.data.repository.impl.StudySessionRepositoryImpl  Result Mcom.homework.assistant.shared.data.repository.impl.StudySessionRepositoryImpl  StudyStatistics Mcom.homework.assistant.shared.data.repository.impl.StudySessionRepositoryImpl  Unit Mcom.homework.assistant.shared.data.repository.impl.StudySessionRepositoryImpl  	emptyList Mcom.homework.assistant.shared.data.repository.impl.StudySessionRepositoryImpl  failure Mcom.homework.assistant.shared.data.repository.impl.StudySessionRepositoryImpl  localDataSource Mcom.homework.assistant.shared.data.repository.impl.StudySessionRepositoryImpl  success Mcom.homework.assistant.shared.data.repository.impl.StudySessionRepositoryImpl  Result Ecom.homework.assistant.shared.data.repository.impl.WordRepositoryImpl  Unit Ecom.homework.assistant.shared.data.repository.impl.WordRepositoryImpl  failure Ecom.homework.assistant.shared.data.repository.impl.WordRepositoryImpl  localDataSource Ecom.homework.assistant.shared.data.repository.impl.WordRepositoryImpl  success Ecom.homework.assistant.shared.data.repository.impl.WordRepositoryImpl  Boolean 'com.homework.assistant.shared.dictation  DictationProgress 'com.homework.assistant.shared.dictation  DictationResult 'com.homework.assistant.shared.dictation  DictationService 'com.homework.assistant.shared.dictation  DictationSession 'com.homework.assistant.shared.dictation  DictationSessionState 'com.homework.assistant.shared.dictation  DictationSettings 'com.homework.assistant.shared.dictation  DictationState 'com.homework.assistant.shared.dictation  	Exception 'com.homework.assistant.shared.dictation  Float 'com.homework.assistant.shared.dictation  Flow 'com.homework.assistant.shared.dictation  IdGenerator 'com.homework.assistant.shared.dictation  Int 'com.homework.assistant.shared.dictation  List 'com.homework.assistant.shared.dictation  Long 'com.homework.assistant.shared.dictation  MutableList 'com.homework.assistant.shared.dictation  MutableStateFlow 'com.homework.assistant.shared.dictation  Result 'com.homework.assistant.shared.dictation  Serializable 'com.homework.assistant.shared.dictation  String 'com.homework.assistant.shared.dictation  StudySession 'com.homework.assistant.shared.dictation  StudyStatus 'com.homework.assistant.shared.dictation  	StudyType 'com.homework.assistant.shared.dictation  System 'com.homework.assistant.shared.dictation  TextToSpeechRequest 'com.homework.assistant.shared.dictation  TextToSpeechService 'com.homework.assistant.shared.dictation  Unit 'com.homework.assistant.shared.dictation  VoiceCommandService 'com.homework.assistant.shared.dictation  VoiceCommandType 'com.homework.assistant.shared.dictation  Word 'com.homework.assistant.shared.dictation  asStateFlow 'com.homework.assistant.shared.dictation  average 'com.homework.assistant.shared.dictation  count 'com.homework.assistant.shared.dictation  	emptyList 'com.homework.assistant.shared.dictation  failure 'com.homework.assistant.shared.dictation  generateSessionId 'com.homework.assistant.shared.dictation  
isNotEmpty 'com.homework.assistant.shared.dictation  
lastOrNull 'com.homework.assistant.shared.dictation  listOf 'com.homework.assistant.shared.dictation  map 'com.homework.assistant.shared.dictation  
mutableListOf 'com.homework.assistant.shared.dictation  success 'com.homework.assistant.shared.dictation  sumOf 'com.homework.assistant.shared.dictation  trim 'com.homework.assistant.shared.dictation  Float 9com.homework.assistant.shared.dictation.DictationProgress  Int 9com.homework.assistant.shared.dictation.DictationProgress  Long 9com.homework.assistant.shared.dictation.DictationProgress  correctCount 9com.homework.assistant.shared.dictation.DictationProgress  currentIndex 9com.homework.assistant.shared.dictation.DictationProgress  
totalCount 9com.homework.assistant.shared.dictation.DictationProgress  
wrongCount 9com.homework.assistant.shared.dictation.DictationProgress  Boolean 7com.homework.assistant.shared.dictation.DictationResult  Int 7com.homework.assistant.shared.dictation.DictationResult  Long 7com.homework.assistant.shared.dictation.DictationResult  String 7com.homework.assistant.shared.dictation.DictationResult  System 7com.homework.assistant.shared.dictation.DictationResult  Word 7com.homework.assistant.shared.dictation.DictationResult  	hintsUsed 7com.homework.assistant.shared.dictation.DictationResult  	isCorrect 7com.homework.assistant.shared.dictation.DictationResult  score 7com.homework.assistant.shared.dictation.DictationResult  	timeSpent 7com.homework.assistant.shared.dictation.DictationResult  System Acom.homework.assistant.shared.dictation.DictationResult.Companion  DictationSettings 8com.homework.assistant.shared.dictation.DictationService  DictationResult 8com.homework.assistant.shared.dictation.DictationSession  DictationSessionState 8com.homework.assistant.shared.dictation.DictationSession  DictationSettings 8com.homework.assistant.shared.dictation.DictationSession  Int 8com.homework.assistant.shared.dictation.DictationSession  List 8com.homework.assistant.shared.dictation.DictationSession  Long 8com.homework.assistant.shared.dictation.DictationSession  MutableList 8com.homework.assistant.shared.dictation.DictationSession  String 8com.homework.assistant.shared.dictation.DictationSession  System 8com.homework.assistant.shared.dictation.DictationSession  Word 8com.homework.assistant.shared.dictation.DictationSession  currentIndex 8com.homework.assistant.shared.dictation.DictationSession  endTime 8com.homework.assistant.shared.dictation.DictationSession  id 8com.homework.assistant.shared.dictation.DictationSession  
mutableListOf 8com.homework.assistant.shared.dictation.DictationSession  results 8com.homework.assistant.shared.dictation.DictationSession  settings 8com.homework.assistant.shared.dictation.DictationSession  	startTime 8com.homework.assistant.shared.dictation.DictationSession  state 8com.homework.assistant.shared.dictation.DictationSession  words 8com.homework.assistant.shared.dictation.DictationSession  DictationSessionState Bcom.homework.assistant.shared.dictation.DictationSession.Companion  System Bcom.homework.assistant.shared.dictation.DictationSession.Companion  
mutableListOf Bcom.homework.assistant.shared.dictation.DictationSession.Companion  CHECKING =com.homework.assistant.shared.dictation.DictationSessionState  	COMPLETED =com.homework.assistant.shared.dictation.DictationSessionState  	Companion =com.homework.assistant.shared.dictation.DictationSessionState  	LISTENING =com.homework.assistant.shared.dictation.DictationSessionState  PAUSED =com.homework.assistant.shared.dictation.DictationSessionState  READY =com.homework.assistant.shared.dictation.DictationSessionState  SPEAKING =com.homework.assistant.shared.dictation.DictationSessionState  Boolean 9com.homework.assistant.shared.dictation.DictationSettings  Float 9com.homework.assistant.shared.dictation.DictationSettings  Int 9com.homework.assistant.shared.dictation.DictationSettings  Long 9com.homework.assistant.shared.dictation.DictationSettings  autoNext 9com.homework.assistant.shared.dictation.DictationSettings  
speakingSpeed 9com.homework.assistant.shared.dictation.DictationSettings  Boolean 6com.homework.assistant.shared.dictation.DictationState  DictationProgress 6com.homework.assistant.shared.dictation.DictationState  DictationResult 6com.homework.assistant.shared.dictation.DictationState  DictationSession 6com.homework.assistant.shared.dictation.DictationState  List 6com.homework.assistant.shared.dictation.DictationState  String 6com.homework.assistant.shared.dictation.DictationState  Word 6com.homework.assistant.shared.dictation.DictationState  	emptyList 6com.homework.assistant.shared.dictation.DictationState  	emptyList @com.homework.assistant.shared.dictation.DictationState.Companion  Boolean ,com.homework.assistant.shared.dictation.impl  DictationProgress ,com.homework.assistant.shared.dictation.impl  DictationResult ,com.homework.assistant.shared.dictation.impl  DictationService ,com.homework.assistant.shared.dictation.impl  DictationSession ,com.homework.assistant.shared.dictation.impl  DictationSessionState ,com.homework.assistant.shared.dictation.impl  DictationSettings ,com.homework.assistant.shared.dictation.impl  DictationState ,com.homework.assistant.shared.dictation.impl  	Exception ,com.homework.assistant.shared.dictation.impl  Flow ,com.homework.assistant.shared.dictation.impl  IdGenerator ,com.homework.assistant.shared.dictation.impl  Int ,com.homework.assistant.shared.dictation.impl  List ,com.homework.assistant.shared.dictation.impl  MutableStateFlow ,com.homework.assistant.shared.dictation.impl  Result ,com.homework.assistant.shared.dictation.impl  String ,com.homework.assistant.shared.dictation.impl  StudySession ,com.homework.assistant.shared.dictation.impl  StudyStatus ,com.homework.assistant.shared.dictation.impl  	StudyType ,com.homework.assistant.shared.dictation.impl  System ,com.homework.assistant.shared.dictation.impl  TextToSpeechRequest ,com.homework.assistant.shared.dictation.impl  TextToSpeechService ,com.homework.assistant.shared.dictation.impl  Unit ,com.homework.assistant.shared.dictation.impl  VoiceCommandService ,com.homework.assistant.shared.dictation.impl  VoiceCommandType ,com.homework.assistant.shared.dictation.impl  Word ,com.homework.assistant.shared.dictation.impl  asStateFlow ,com.homework.assistant.shared.dictation.impl  average ,com.homework.assistant.shared.dictation.impl  count ,com.homework.assistant.shared.dictation.impl  failure ,com.homework.assistant.shared.dictation.impl  generateSessionId ,com.homework.assistant.shared.dictation.impl  
isNotEmpty ,com.homework.assistant.shared.dictation.impl  
lastOrNull ,com.homework.assistant.shared.dictation.impl  listOf ,com.homework.assistant.shared.dictation.impl  map ,com.homework.assistant.shared.dictation.impl  success ,com.homework.assistant.shared.dictation.impl  sumOf ,com.homework.assistant.shared.dictation.impl  trim ,com.homework.assistant.shared.dictation.impl  DictationProgress Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  DictationResult Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  DictationSession Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  DictationSessionState Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  DictationState Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  	Exception Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  IdGenerator Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  MutableStateFlow Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  Result Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  StudySession Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  StudyStatus Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  	StudyType Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  System Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  TextToSpeechRequest Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  Unit Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  VoiceCommandType Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  _dictationState Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  asStateFlow Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  average Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  calculateOverallScore Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  calculateScore Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  checkAnswer Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  count Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  currentSession Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  failure Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  finishDictation Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  generateSessionId Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  getAvailableCommands Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  getCurrentWord Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  getProgress Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  
isNotEmpty Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  
lastOrNull Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  listOf Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  map Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  nextWord Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  pauseDictation Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  previousWord Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  repeatCurrentWord Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  resumeDictation Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  showWordMeaning Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  speakCurrentWord Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  speakWordPinyin Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  success Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  sumOf Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  textToSpeechService Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  trim Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  updateState Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  voiceCommandService Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  Boolean (com.homework.assistant.shared.recitation  	Exception (com.homework.assistant.shared.recitation  Float (com.homework.assistant.shared.recitation  Flow (com.homework.assistant.shared.recitation  IdGenerator (com.homework.assistant.shared.recitation  Int (com.homework.assistant.shared.recitation  List (com.homework.assistant.shared.recitation  Long (com.homework.assistant.shared.recitation  MutableList (com.homework.assistant.shared.recitation  MutableStateFlow (com.homework.assistant.shared.recitation  
Recitation (com.homework.assistant.shared.recitation  RecitationAttempt (com.homework.assistant.shared.recitation  RecitationProgress (com.homework.assistant.shared.recitation  RecitationService (com.homework.assistant.shared.recitation  RecitationSession (com.homework.assistant.shared.recitation  RecitationSessionState (com.homework.assistant.shared.recitation  RecitationSettings (com.homework.assistant.shared.recitation  RecitationState (com.homework.assistant.shared.recitation  Result (com.homework.assistant.shared.recitation  SemanticAnalysisService (com.homework.assistant.shared.recitation  Serializable (com.homework.assistant.shared.recitation  String (com.homework.assistant.shared.recitation  StudySession (com.homework.assistant.shared.recitation  StudyStatus (com.homework.assistant.shared.recitation  	StudyType (com.homework.assistant.shared.recitation  System (com.homework.assistant.shared.recitation  TextToSpeechRequest (com.homework.assistant.shared.recitation  TextToSpeechService (com.homework.assistant.shared.recitation  Unit (com.homework.assistant.shared.recitation  VoiceCommandService (com.homework.assistant.shared.recitation  VoiceCommandType (com.homework.assistant.shared.recitation  asStateFlow (com.homework.assistant.shared.recitation  average (com.homework.assistant.shared.recitation  count (com.homework.assistant.shared.recitation  	emptyList (com.homework.assistant.shared.recitation  failure (com.homework.assistant.shared.recitation  filter (com.homework.assistant.shared.recitation  generateSessionId (com.homework.assistant.shared.recitation  
isNotEmpty (com.homework.assistant.shared.recitation  
lastOrNull (com.homework.assistant.shared.recitation  listOf (com.homework.assistant.shared.recitation  map (com.homework.assistant.shared.recitation  
mutableListOf (com.homework.assistant.shared.recitation  split (com.homework.assistant.shared.recitation  success (com.homework.assistant.shared.recitation  sumOf (com.homework.assistant.shared.recitation  toRegex (com.homework.assistant.shared.recitation  trim (com.homework.assistant.shared.recitation  Boolean :com.homework.assistant.shared.recitation.RecitationAttempt  Float :com.homework.assistant.shared.recitation.RecitationAttempt  Int :com.homework.assistant.shared.recitation.RecitationAttempt  Long :com.homework.assistant.shared.recitation.RecitationAttempt  String :com.homework.assistant.shared.recitation.RecitationAttempt  System :com.homework.assistant.shared.recitation.RecitationAttempt  expectedText :com.homework.assistant.shared.recitation.RecitationAttempt  	hintsUsed :com.homework.assistant.shared.recitation.RecitationAttempt  	isCorrect :com.homework.assistant.shared.recitation.RecitationAttempt  score :com.homework.assistant.shared.recitation.RecitationAttempt  	timeSpent :com.homework.assistant.shared.recitation.RecitationAttempt  System Dcom.homework.assistant.shared.recitation.RecitationAttempt.Companion  Float ;com.homework.assistant.shared.recitation.RecitationProgress  Int ;com.homework.assistant.shared.recitation.RecitationProgress  Long ;com.homework.assistant.shared.recitation.RecitationProgress  completedSentences ;com.homework.assistant.shared.recitation.RecitationProgress  correctAttempts ;com.homework.assistant.shared.recitation.RecitationProgress  
totalAttempts ;com.homework.assistant.shared.recitation.RecitationProgress  totalSentences ;com.homework.assistant.shared.recitation.RecitationProgress  RecitationSettings :com.homework.assistant.shared.recitation.RecitationService  Int :com.homework.assistant.shared.recitation.RecitationSession  Long :com.homework.assistant.shared.recitation.RecitationSession  MutableList :com.homework.assistant.shared.recitation.RecitationSession  
Recitation :com.homework.assistant.shared.recitation.RecitationSession  RecitationAttempt :com.homework.assistant.shared.recitation.RecitationSession  RecitationSessionState :com.homework.assistant.shared.recitation.RecitationSession  RecitationSettings :com.homework.assistant.shared.recitation.RecitationSession  String :com.homework.assistant.shared.recitation.RecitationSession  System :com.homework.assistant.shared.recitation.RecitationSession  attempts :com.homework.assistant.shared.recitation.RecitationSession  currentProgress :com.homework.assistant.shared.recitation.RecitationSession  currentSentenceIndex :com.homework.assistant.shared.recitation.RecitationSession  endTime :com.homework.assistant.shared.recitation.RecitationSession  id :com.homework.assistant.shared.recitation.RecitationSession  
mutableListOf :com.homework.assistant.shared.recitation.RecitationSession  
recitation :com.homework.assistant.shared.recitation.RecitationSession  settings :com.homework.assistant.shared.recitation.RecitationSession  	startTime :com.homework.assistant.shared.recitation.RecitationSession  state :com.homework.assistant.shared.recitation.RecitationSession  RecitationSessionState Dcom.homework.assistant.shared.recitation.RecitationSession.Companion  System Dcom.homework.assistant.shared.recitation.RecitationSession.Companion  
mutableListOf Dcom.homework.assistant.shared.recitation.RecitationSession.Companion  CHECKING ?com.homework.assistant.shared.recitation.RecitationSessionState  	COMPLETED ?com.homework.assistant.shared.recitation.RecitationSessionState  	Companion ?com.homework.assistant.shared.recitation.RecitationSessionState  	LISTENING ?com.homework.assistant.shared.recitation.RecitationSessionState  PAUSED ?com.homework.assistant.shared.recitation.RecitationSessionState  	PROMPTING ?com.homework.assistant.shared.recitation.RecitationSessionState  READY ?com.homework.assistant.shared.recitation.RecitationSessionState  Boolean ;com.homework.assistant.shared.recitation.RecitationSettings  Float ;com.homework.assistant.shared.recitation.RecitationSettings  Int ;com.homework.assistant.shared.recitation.RecitationSettings  	allowSkip ;com.homework.assistant.shared.recitation.RecitationSettings  autoNext ;com.homework.assistant.shared.recitation.RecitationSettings  enableHints ;com.homework.assistant.shared.recitation.RecitationSettings  maxHints ;com.homework.assistant.shared.recitation.RecitationSettings  promptSpeed ;com.homework.assistant.shared.recitation.RecitationSettings  similarityThreshold ;com.homework.assistant.shared.recitation.RecitationSettings  Boolean 8com.homework.assistant.shared.recitation.RecitationState  List 8com.homework.assistant.shared.recitation.RecitationState  RecitationAttempt 8com.homework.assistant.shared.recitation.RecitationState  RecitationProgress 8com.homework.assistant.shared.recitation.RecitationState  RecitationSession 8com.homework.assistant.shared.recitation.RecitationState  String 8com.homework.assistant.shared.recitation.RecitationState  	emptyList 8com.homework.assistant.shared.recitation.RecitationState  	emptyList Bcom.homework.assistant.shared.recitation.RecitationState.Companion  	Exception -com.homework.assistant.shared.recitation.impl  Flow -com.homework.assistant.shared.recitation.impl  IdGenerator -com.homework.assistant.shared.recitation.impl  Int -com.homework.assistant.shared.recitation.impl  List -com.homework.assistant.shared.recitation.impl  MutableStateFlow -com.homework.assistant.shared.recitation.impl  
Recitation -com.homework.assistant.shared.recitation.impl  RecitationAttempt -com.homework.assistant.shared.recitation.impl  RecitationProgress -com.homework.assistant.shared.recitation.impl  RecitationService -com.homework.assistant.shared.recitation.impl  RecitationSession -com.homework.assistant.shared.recitation.impl  RecitationSessionState -com.homework.assistant.shared.recitation.impl  RecitationSettings -com.homework.assistant.shared.recitation.impl  RecitationState -com.homework.assistant.shared.recitation.impl  Result -com.homework.assistant.shared.recitation.impl  SemanticAnalysisService -com.homework.assistant.shared.recitation.impl  String -com.homework.assistant.shared.recitation.impl  StudySession -com.homework.assistant.shared.recitation.impl  StudyStatus -com.homework.assistant.shared.recitation.impl  	StudyType -com.homework.assistant.shared.recitation.impl  System -com.homework.assistant.shared.recitation.impl  TextToSpeechRequest -com.homework.assistant.shared.recitation.impl  TextToSpeechService -com.homework.assistant.shared.recitation.impl  Unit -com.homework.assistant.shared.recitation.impl  VoiceCommandService -com.homework.assistant.shared.recitation.impl  VoiceCommandType -com.homework.assistant.shared.recitation.impl  asStateFlow -com.homework.assistant.shared.recitation.impl  average -com.homework.assistant.shared.recitation.impl  count -com.homework.assistant.shared.recitation.impl  failure -com.homework.assistant.shared.recitation.impl  filter -com.homework.assistant.shared.recitation.impl  generateSessionId -com.homework.assistant.shared.recitation.impl  
isNotEmpty -com.homework.assistant.shared.recitation.impl  
lastOrNull -com.homework.assistant.shared.recitation.impl  listOf -com.homework.assistant.shared.recitation.impl  map -com.homework.assistant.shared.recitation.impl  split -com.homework.assistant.shared.recitation.impl  success -com.homework.assistant.shared.recitation.impl  sumOf -com.homework.assistant.shared.recitation.impl  toRegex -com.homework.assistant.shared.recitation.impl  trim -com.homework.assistant.shared.recitation.impl  	Exception Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  IdGenerator Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  MutableStateFlow Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  RecitationAttempt Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  RecitationProgress Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  RecitationSession Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  RecitationSessionState Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  RecitationState Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  Result Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  StudySession Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  StudyStatus Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  	StudyType Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  System Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  TextToSpeechRequest Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  Unit Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  VoiceCommandType Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  _recitationState Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  asStateFlow Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  average Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  calculateOverallScore Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  count Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  currentSession Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  failure Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  filter Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  finishRecitation Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  generateSessionId Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  getAvailableCommands Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  getCurrentSentence Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  getProgress Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  giveHint Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  
isNotEmpty Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  
lastOrNull Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  listOf Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  map Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  nextSentence Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  pauseRecitation Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  previousSentence Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  repeatCurrentSentence Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  restartRecitation Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  resumeRecitation Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  semanticAnalysisService Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  speakCurrentSentence Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  split Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  splitIntoSentences Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  success Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  sumOf Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  textToSpeechService Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  toRegex Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  trim Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  updateState Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  voiceCommandService Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  Boolean #com.homework.assistant.shared.utils  Clock #com.homework.assistant.shared.utils  DateTimePeriod #com.homework.assistant.shared.utils  IdGenerator #com.homework.assistant.shared.utils  Instant #com.homework.assistant.shared.utils  Int #com.homework.assistant.shared.utils  Long #com.homework.assistant.shared.utils  Random #com.homework.assistant.shared.utils  String #com.homework.assistant.shared.utils  TimeZone #com.homework.assistant.shared.utils  atTime #com.homework.assistant.shared.utils  currentSystemDefault #com.homework.assistant.shared.utils  	daysUntil #com.homework.assistant.shared.utils  fromEpochMilliseconds #com.homework.assistant.shared.utils  joinToString #com.homework.assistant.shared.utils  map #com.homework.assistant.shared.utils  minus #com.homework.assistant.shared.utils  nextInt #com.homework.assistant.shared.utils  now #com.homework.assistant.shared.utils  padStart #com.homework.assistant.shared.utils  	toInstant #com.homework.assistant.shared.utils  toLocalDateTime #com.homework.assistant.shared.utils  Clock -com.homework.assistant.shared.utils.DateUtils  DateTimePeriod -com.homework.assistant.shared.utils.DateUtils  Instant -com.homework.assistant.shared.utils.DateUtils  TimeZone -com.homework.assistant.shared.utils.DateUtils  atTime -com.homework.assistant.shared.utils.DateUtils  currentSystemDefault -com.homework.assistant.shared.utils.DateUtils  	daysUntil -com.homework.assistant.shared.utils.DateUtils  
formatDate -com.homework.assistant.shared.utils.DateUtils  
formatTime -com.homework.assistant.shared.utils.DateUtils  fromEpochMilliseconds -com.homework.assistant.shared.utils.DateUtils  minus -com.homework.assistant.shared.utils.DateUtils  now -com.homework.assistant.shared.utils.DateUtils  padStart -com.homework.assistant.shared.utils.DateUtils  	toInstant -com.homework.assistant.shared.utils.DateUtils  toLocalDateTime -com.homework.assistant.shared.utils.DateUtils  CHARS /com.homework.assistant.shared.utils.IdGenerator  Random /com.homework.assistant.shared.utils.IdGenerator  
generateId /com.homework.assistant.shared.utils.IdGenerator  generateIdWithPrefix /com.homework.assistant.shared.utils.IdGenerator  generateSessionId /com.homework.assistant.shared.utils.IdGenerator  joinToString /com.homework.assistant.shared.utils.IdGenerator  map /com.homework.assistant.shared.utils.IdGenerator  nextInt /com.homework.assistant.shared.utils.IdGenerator  Boolean #com.homework.assistant.shared.voice  Float #com.homework.assistant.shared.voice  Flow #com.homework.assistant.shared.voice  List #com.homework.assistant.shared.voice  Long #com.homework.assistant.shared.voice  PlaybackProgress #com.homework.assistant.shared.voice  SpeechRecognitionResult #com.homework.assistant.shared.voice  String #com.homework.assistant.shared.voice  TextToSpeechRequest #com.homework.assistant.shared.voice  TextToSpeechResult #com.homework.assistant.shared.voice  TextToSpeechService #com.homework.assistant.shared.voice  VoiceCommand #com.homework.assistant.shared.voice  VoiceCommandService #com.homework.assistant.shared.voice  VoiceGender #com.homework.assistant.shared.voice  	VoiceInfo #com.homework.assistant.shared.voice  VoiceQuality #com.homework.assistant.shared.voice  VoiceRecognitionService #com.homework.assistant.shared.voice  speak 7com.homework.assistant.shared.voice.TextToSpeechService  recognizeCommand 7com.homework.assistant.shared.voice.VoiceCommandService  FEMALE /com.homework.assistant.shared.voice.VoiceGender  MALE /com.homework.assistant.shared.voice.VoiceGender  HIGH 0com.homework.assistant.shared.voice.VoiceQuality  NORMAL 0com.homework.assistant.shared.voice.VoiceQuality  AndroidTextToSpeechService (com.homework.assistant.shared.voice.impl  AndroidVoiceRecognitionService (com.homework.assistant.shared.voice.impl  Boolean (com.homework.assistant.shared.voice.impl  	Exception (com.homework.assistant.shared.voice.impl  Float (com.homework.assistant.shared.voice.impl  Flow (com.homework.assistant.shared.voice.impl  List (com.homework.assistant.shared.voice.impl  Long (com.homework.assistant.shared.voice.impl  PlaybackProgress (com.homework.assistant.shared.voice.impl  SpeechRecognitionResult (com.homework.assistant.shared.voice.impl  String (com.homework.assistant.shared.voice.impl  TextToSpeechRequest (com.homework.assistant.shared.voice.impl  TextToSpeechResult (com.homework.assistant.shared.voice.impl  TextToSpeechService (com.homework.assistant.shared.voice.impl  VoiceCommand (com.homework.assistant.shared.voice.impl  VoiceCommandService (com.homework.assistant.shared.voice.impl  VoiceCommandType (com.homework.assistant.shared.voice.impl  VoiceGender (com.homework.assistant.shared.voice.impl  	VoiceInfo (com.homework.assistant.shared.voice.impl  VoiceQuality (com.homework.assistant.shared.voice.impl  VoiceRecognitionService (com.homework.assistant.shared.voice.impl  any (com.homework.assistant.shared.voice.impl  coerceIn (com.homework.assistant.shared.voice.impl  
component1 (com.homework.assistant.shared.voice.impl  
component2 (com.homework.assistant.shared.voice.impl  contains (com.homework.assistant.shared.voice.impl  equals (com.homework.assistant.shared.voice.impl  filter (com.homework.assistant.shared.voice.impl  flow (com.homework.assistant.shared.voice.impl  
isRecognizing (com.homework.assistant.shared.voice.impl  
isSpeaking (com.homework.assistant.shared.voice.impl  iterator (com.homework.assistant.shared.voice.impl  listOf (com.homework.assistant.shared.voice.impl  map (com.homework.assistant.shared.voice.impl  mapOf (com.homework.assistant.shared.voice.impl  maxByOrNull (com.homework.assistant.shared.voice.impl  mutableMapOf (com.homework.assistant.shared.voice.impl  plus (com.homework.assistant.shared.voice.impl  replace (com.homework.assistant.shared.voice.impl  set (com.homework.assistant.shared.voice.impl  split (com.homework.assistant.shared.voice.impl  to (com.homework.assistant.shared.voice.impl  toList (com.homework.assistant.shared.voice.impl  trim (com.homework.assistant.shared.voice.impl  PlaybackProgress Ccom.homework.assistant.shared.voice.impl.AndroidTextToSpeechService  TextToSpeechRequest Ccom.homework.assistant.shared.voice.impl.AndroidTextToSpeechService  TextToSpeechResult Ccom.homework.assistant.shared.voice.impl.AndroidTextToSpeechService  VoiceGender Ccom.homework.assistant.shared.voice.impl.AndroidTextToSpeechService  	VoiceInfo Ccom.homework.assistant.shared.voice.impl.AndroidTextToSpeechService  VoiceQuality Ccom.homework.assistant.shared.voice.impl.AndroidTextToSpeechService  flow Ccom.homework.assistant.shared.voice.impl.AndroidTextToSpeechService  isPaused Ccom.homework.assistant.shared.voice.impl.AndroidTextToSpeechService  
isSpeaking Ccom.homework.assistant.shared.voice.impl.AndroidTextToSpeechService  listOf Ccom.homework.assistant.shared.voice.impl.AndroidTextToSpeechService  speak Ccom.homework.assistant.shared.voice.impl.AndroidTextToSpeechService  SpeechRecognitionResult Gcom.homework.assistant.shared.voice.impl.AndroidVoiceRecognitionService  flow Gcom.homework.assistant.shared.voice.impl.AndroidVoiceRecognitionService  
isRecognizing Gcom.homework.assistant.shared.voice.impl.AndroidVoiceRecognitionService  VoiceCommand @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  VoiceCommandType @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  any @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  calculateConfidence @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  coerceIn @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  commandPatterns @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  
component1 @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  
component2 @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  confidenceThreshold @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  contains @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  customPatterns @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  equals @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  filter @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  iterator @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  map @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  mapOf @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  matchesPattern @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  maxByOrNull @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  mutableMapOf @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  plus @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  recognizeCommand @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  replace @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  set @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  split @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  to @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  toList @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  trim @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  Boolean )com.homework.assistant.shared.voice.model  Float )com.homework.assistant.shared.voice.model  List )com.homework.assistant.shared.voice.model  Long )com.homework.assistant.shared.voice.model  Map )com.homework.assistant.shared.voice.model  Serializable )com.homework.assistant.shared.voice.model  SpeechRecognitionResult )com.homework.assistant.shared.voice.model  String )com.homework.assistant.shared.voice.model  System )com.homework.assistant.shared.voice.model  TextToSpeechRequest )com.homework.assistant.shared.voice.model  TextToSpeechResult )com.homework.assistant.shared.voice.model  VoiceCommand )com.homework.assistant.shared.voice.model  VoiceCommandType )com.homework.assistant.shared.voice.model  	emptyList )com.homework.assistant.shared.voice.model  emptyMap )com.homework.assistant.shared.voice.model  Boolean Acom.homework.assistant.shared.voice.model.SpeechRecognitionResult  Float Acom.homework.assistant.shared.voice.model.SpeechRecognitionResult  List Acom.homework.assistant.shared.voice.model.SpeechRecognitionResult  Long Acom.homework.assistant.shared.voice.model.SpeechRecognitionResult  String Acom.homework.assistant.shared.voice.model.SpeechRecognitionResult  System Acom.homework.assistant.shared.voice.model.SpeechRecognitionResult  	emptyList Acom.homework.assistant.shared.voice.model.SpeechRecognitionResult  System Kcom.homework.assistant.shared.voice.model.SpeechRecognitionResult.Companion  	emptyList Kcom.homework.assistant.shared.voice.model.SpeechRecognitionResult.Companion  Float =com.homework.assistant.shared.voice.model.TextToSpeechRequest  String =com.homework.assistant.shared.voice.model.TextToSpeechRequest  text =com.homework.assistant.shared.voice.model.TextToSpeechRequest  Boolean <com.homework.assistant.shared.voice.model.TextToSpeechResult  Long <com.homework.assistant.shared.voice.model.TextToSpeechResult  String <com.homework.assistant.shared.voice.model.TextToSpeechResult  error <com.homework.assistant.shared.voice.model.TextToSpeechResult  success <com.homework.assistant.shared.voice.model.TextToSpeechResult  Float 6com.homework.assistant.shared.voice.model.VoiceCommand  Long 6com.homework.assistant.shared.voice.model.VoiceCommand  Map 6com.homework.assistant.shared.voice.model.VoiceCommand  String 6com.homework.assistant.shared.voice.model.VoiceCommand  System 6com.homework.assistant.shared.voice.model.VoiceCommand  VoiceCommandType 6com.homework.assistant.shared.voice.model.VoiceCommand  emptyMap 6com.homework.assistant.shared.voice.model.VoiceCommand  type 6com.homework.assistant.shared.voice.model.VoiceCommand  System @com.homework.assistant.shared.voice.model.VoiceCommand.Companion  emptyMap @com.homework.assistant.shared.voice.model.VoiceCommand.Companion  CANCEL :com.homework.assistant.shared.voice.model.VoiceCommandType  CONFIRM :com.homework.assistant.shared.voice.model.VoiceCommandType  	Companion :com.homework.assistant.shared.voice.model.VoiceCommandType  FINISH_DICTATION :com.homework.assistant.shared.voice.model.VoiceCommandType  FINISH_RECITATION :com.homework.assistant.shared.voice.model.VoiceCommandType  	GIVE_HINT :com.homework.assistant.shared.voice.model.VoiceCommandType  HELP :com.homework.assistant.shared.voice.model.VoiceCommandType  
NEXT_SENTENCE :com.homework.assistant.shared.voice.model.VoiceCommandType  	NEXT_WORD :com.homework.assistant.shared.voice.model.VoiceCommandType  PAUSE_DICTATION :com.homework.assistant.shared.voice.model.VoiceCommandType  PAUSE_RECITATION :com.homework.assistant.shared.voice.model.VoiceCommandType  PREVIOUS_SENTENCE :com.homework.assistant.shared.voice.model.VoiceCommandType  
PREVIOUS_WORD :com.homework.assistant.shared.voice.model.VoiceCommandType  REPEAT_SENTENCE :com.homework.assistant.shared.voice.model.VoiceCommandType  REPEAT_WORD :com.homework.assistant.shared.voice.model.VoiceCommandType  RESTART_RECITATION :com.homework.assistant.shared.voice.model.VoiceCommandType  RESUME_DICTATION :com.homework.assistant.shared.voice.model.VoiceCommandType  RESUME_RECITATION :com.homework.assistant.shared.voice.model.VoiceCommandType  SHOW_MEANING :com.homework.assistant.shared.voice.model.VoiceCommandType  
SPELL_WORD :com.homework.assistant.shared.voice.model.VoiceCommandType  START_RECITATION :com.homework.assistant.shared.voice.model.VoiceCommandType  UNKNOWN :com.homework.assistant.shared.voice.model.VoiceCommandType  valueOf :com.homework.assistant.shared.voice.model.VoiceCommandType  	ByteArray /homeworkassistantkmp.shared.generated.resources  ExperimentalResourceApi /homeworkassistantkmp.shared.generated.resources  OptIn /homeworkassistantkmp.shared.generated.resources  String /homeworkassistantkmp.shared.generated.resources  getResourceUri /homeworkassistantkmp.shared.generated.resources  org /homeworkassistantkmp.shared.generated.resources  readResourceBytes /homeworkassistantkmp.shared.generated.resources  	ByteArray 3homeworkassistantkmp.shared.generated.resources.Res  ExperimentalResourceApi 3homeworkassistantkmp.shared.generated.resources.Res  String 3homeworkassistantkmp.shared.generated.resources.Res  getResourceUri 3homeworkassistantkmp.shared.generated.resources.Res  readResourceBytes 3homeworkassistantkmp.shared.generated.resources.Res  	Exception 	java.lang  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  	Exception kotlin  
FloatArray kotlin  	Function1 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  
ShortArray kotlin  String kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  map kotlin  plus kotlin  to kotlin  toList kotlin  equals 
kotlin.Any  not kotlin.Boolean  toInt 
kotlin.Double  toLong 
kotlin.Double  message kotlin.Exception  coerceIn kotlin.Float  	compareTo kotlin.Float  div kotlin.Float  plus kotlin.Float  times kotlin.Float  toInt kotlin.Float  	compareTo 
kotlin.Int  dec 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  toString 
kotlin.Int  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  rangeTo kotlin.Long  rem kotlin.Long  	Companion 
kotlin.Result  failure 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  contains 
kotlin.String  equals 
kotlin.String  get 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  length 
kotlin.String  padStart 
kotlin.String  plus 
kotlin.String  replace 
kotlin.String  split 
kotlin.String  to 
kotlin.String  toRegex 
kotlin.String  trim 
kotlin.String  message kotlin.Throwable  ByteIterator kotlin.collections  CharIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableCollection kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  any kotlin.collections  average kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  count kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  filter kotlin.collections  forEach kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  iterator kotlin.collections  joinToString kotlin.collections  
lastOrNull kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  maxByOrNull kotlin.collections  mutableIterator kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  plus kotlin.collections  set kotlin.collections  sumOf kotlin.collections  sumOfInt kotlin.collections  take kotlin.collections  toList kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  any kotlin.collections.List  average kotlin.collections.List  filter kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  joinToString kotlin.collections.List  map kotlin.collections.List  maxByOrNull kotlin.collections.List  plus kotlin.collections.List  size kotlin.collections.List  take kotlin.collections.List  Entry kotlin.collections.Map  iterator kotlin.collections.Map  keys kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  count $kotlin.collections.MutableCollection  filter $kotlin.collections.MutableCollection  toList $kotlin.collections.MutableCollection  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  clear kotlin.collections.MutableList  count kotlin.collections.MutableList  isEmpty kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  
lastOrNull kotlin.collections.MutableList  map kotlin.collections.MutableList  size kotlin.collections.MutableList  sumOf kotlin.collections.MutableList  MutableEntry kotlin.collections.MutableMap  get kotlin.collections.MutableMap  iterator kotlin.collections.MutableMap  keys kotlin.collections.MutableMap  remove kotlin.collections.MutableMap  set kotlin.collections.MutableMap  values kotlin.collections.MutableMap  
component1 *kotlin.collections.MutableMap.MutableEntry  
component2 *kotlin.collections.MutableMap.MutableEntry  toList kotlin.collections.MutableSet  toList kotlin.collections.Set  SuspendFunction1 kotlin.coroutines  iterator 	kotlin.io  Random 
kotlin.random  Default kotlin.random.Random  nextInt kotlin.random.Random  nextInt kotlin.random.Random.Default  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  coerceIn 
kotlin.ranges  contains 
kotlin.ranges  
lastOrNull 
kotlin.ranges  map kotlin.ranges.IntRange  contains kotlin.ranges.LongRange  Sequence kotlin.sequences  any kotlin.sequences  average kotlin.sequences  contains kotlin.sequences  count kotlin.sequences  filter kotlin.sequences  forEach kotlin.sequences  iterator kotlin.sequences  joinToString kotlin.sequences  
lastOrNull kotlin.sequences  map kotlin.sequences  maxByOrNull kotlin.sequences  plus kotlin.sequences  sumOf kotlin.sequences  take kotlin.sequences  toList kotlin.sequences  Regex kotlin.text  any kotlin.text  contains kotlin.text  count kotlin.text  equals kotlin.text  filter kotlin.text  forEach kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  iterator kotlin.text  
lastOrNull kotlin.text  map kotlin.text  maxByOrNull kotlin.text  padStart kotlin.text  plus kotlin.text  replace kotlin.text  set kotlin.text  split kotlin.text  sumOf kotlin.text  take kotlin.text  toList kotlin.text  toRegex kotlin.text  trim kotlin.text  Duration kotlin.time  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  map kotlinx.coroutines.flow  PlaybackProgress %kotlinx.coroutines.flow.FlowCollector  SpeechRecognitionResult %kotlinx.coroutines.flow.FlowCollector  emit %kotlinx.coroutines.flow.FlowCollector  
isRecognizing %kotlinx.coroutines.flow.FlowCollector  
isSpeaking %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  map (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  Boolean kotlinx.datetime  Clock kotlinx.datetime  
DatePeriod kotlinx.datetime  DateTimePeriod kotlinx.datetime  Instant kotlinx.datetime  Int kotlinx.datetime  	LocalDate kotlinx.datetime  
LocalDateTime kotlinx.datetime  	LocalTime kotlinx.datetime  Long kotlinx.datetime  String kotlinx.datetime  TimeZone kotlinx.datetime  atTime kotlinx.datetime  currentSystemDefault kotlinx.datetime  	daysUntil kotlinx.datetime  fromEpochMilliseconds kotlinx.datetime  minus kotlinx.datetime  now kotlinx.datetime  padStart kotlinx.datetime  	toInstant kotlinx.datetime  toLocalDateTime kotlinx.datetime  	Companion kotlinx.datetime.Clock  System kotlinx.datetime.Clock  now kotlinx.datetime.Clock.System  	Companion kotlinx.datetime.Instant  fromEpochMilliseconds kotlinx.datetime.Instant  minus kotlinx.datetime.Instant  toEpochMilliseconds kotlinx.datetime.Instant  toLocalDateTime kotlinx.datetime.Instant  fromEpochMilliseconds "kotlinx.datetime.Instant.Companion  atTime kotlinx.datetime.LocalDate  	daysUntil kotlinx.datetime.LocalDate  toString kotlinx.datetime.LocalDate  date kotlinx.datetime.LocalDateTime  time kotlinx.datetime.LocalDateTime  	toInstant kotlinx.datetime.LocalDateTime  hour kotlinx.datetime.LocalTime  minute kotlinx.datetime.LocalTime  second kotlinx.datetime.LocalTime  	Companion kotlinx.datetime.TimeZone  currentSystemDefault kotlinx.datetime.TimeZone  currentSystemDefault #kotlinx.datetime.TimeZone.Companion  Serializable kotlinx.serialization  ExperimentalResourceApi org.jetbrains.compose.resources  InternalResourceApi org.jetbrains.compose.resources  getResourceUri org.jetbrains.compose.resources  readResourceBytes org.jetbrains.compose.resources                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     