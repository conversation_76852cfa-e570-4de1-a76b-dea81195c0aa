  HintType  com.homework.assistant.shared.ai  Intent  com.homework.assistant.shared.ai  SemanticAnalysisService  com.homework.assistant.shared.ai  SemanticAnalysisServiceImpl  com.homework.assistant.shared.ai  	Sentiment  com.homework.assistant.shared.ai  SimilarityMethod  com.homework.assistant.shared.ai  Test  com.homework.assistant.shared.ai  Unit  com.homework.assistant.shared.ai  any  com.homework.assistant.shared.ai  assertEquals  com.homework.assistant.shared.ai  
assertTrue  com.homework.assistant.shared.ai  
component1  com.homework.assistant.shared.ai  
component2  com.homework.assistant.shared.ai  contains  com.homework.assistant.shared.ai  forEach  com.homework.assistant.shared.ai  
isNotEmpty  com.homework.assistant.shared.ai  kotlinx  com.homework.assistant.shared.ai  mapOf  com.homework.assistant.shared.ai  runTest  com.homework.assistant.shared.ai  to  com.homework.assistant.shared.ai  	NEXT_WORD )com.homework.assistant.shared.ai.HintType  HintType <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  Intent <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  SemanticAnalysisServiceImpl <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  	Sentiment <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  SimilarityMethod <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  any <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  assertEquals <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  
assertTrue <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  
component1 <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  
component2 <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  contains <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  
isNotEmpty <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  mapOf <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  runTest <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  semanticService <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  to <com.homework.assistant.shared.ai.SemanticAnalysisServiceTest  SemanticAnalysisServiceImpl %com.homework.assistant.shared.ai.impl  analyzeSemantics Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  calculateSimilarity Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  correctText Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  extractKeywords Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  generateRecitationHint Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  isRecitationComplete Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  recognizeIntent Acom.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl  Boolean &com.homework.assistant.shared.ai.model  Entity &com.homework.assistant.shared.ai.model  Float &com.homework.assistant.shared.ai.model  IdGenerator &com.homework.assistant.shared.ai.model  Int &com.homework.assistant.shared.ai.model  Intent &com.homework.assistant.shared.ai.model  IntentRecognitionResult &com.homework.assistant.shared.ai.model  List &com.homework.assistant.shared.ai.model  
Recitation &com.homework.assistant.shared.ai.model  RecitationServiceImpl &com.homework.assistant.shared.ai.model  RecitationSessionState &com.homework.assistant.shared.ai.model  SemanticAnalysisResult &com.homework.assistant.shared.ai.model  SemanticAnalysisService &com.homework.assistant.shared.ai.model  	Sentiment &com.homework.assistant.shared.ai.model  SimilarityMethod &com.homework.assistant.shared.ai.model  String &com.homework.assistant.shared.ai.model  Test &com.homework.assistant.shared.ai.model  TextSimilarity &com.homework.assistant.shared.ai.model  TextToSpeechRequest &com.homework.assistant.shared.ai.model  TextToSpeechResult &com.homework.assistant.shared.ai.model  TextToSpeechService &com.homework.assistant.shared.ai.model  Unit &com.homework.assistant.shared.ai.model  VoiceCommand &com.homework.assistant.shared.ai.model  VoiceCommandService &com.homework.assistant.shared.ai.model  VoiceCommandType &com.homework.assistant.shared.ai.model  assertEquals &com.homework.assistant.shared.ai.model  
assertNotNull &com.homework.assistant.shared.ai.model  
assertTrue &com.homework.assistant.shared.ai.model  com &com.homework.assistant.shared.ai.model  contains &com.homework.assistant.shared.ai.model  	emptyList &com.homework.assistant.shared.ai.model  first &com.homework.assistant.shared.ai.model  generateRecitationId &com.homework.assistant.shared.ai.model  
isNotEmpty &com.homework.assistant.shared.ai.model  kotlinx &com.homework.assistant.shared.ai.model  map &com.homework.assistant.shared.ai.model  runTest &com.homework.assistant.shared.ai.model  trim &com.homework.assistant.shared.ai.model  ASK_QUESTION -com.homework.assistant.shared.ai.model.Intent  
CANCEL_ACTION -com.homework.assistant.shared.ai.model.Intent  CONFIRM_ACTION -com.homework.assistant.shared.ai.model.Intent  	Companion -com.homework.assistant.shared.ai.model.Intent  REQUEST_HELP -com.homework.assistant.shared.ai.model.Intent  REQUEST_HINT -com.homework.assistant.shared.ai.model.Intent  START_DICTATION -com.homework.assistant.shared.ai.model.Intent  START_RECITATION -com.homework.assistant.shared.ai.model.Intent  UNKNOWN -com.homework.assistant.shared.ai.model.Intent  
confidence >com.homework.assistant.shared.ai.model.IntentRecognitionResult  intent >com.homework.assistant.shared.ai.model.IntentRecognitionResult  
confidence =com.homework.assistant.shared.ai.model.SemanticAnalysisResult  keywords =com.homework.assistant.shared.ai.model.SemanticAnalysisResult  originalText =com.homework.assistant.shared.ai.model.SemanticAnalysisResult  processingTime =com.homework.assistant.shared.ai.model.SemanticAnalysisResult  	sentiment =com.homework.assistant.shared.ai.model.SemanticAnalysisResult  	Companion 0com.homework.assistant.shared.ai.model.Sentiment  NEUTRAL 0com.homework.assistant.shared.ai.model.Sentiment  POSITIVE 0com.homework.assistant.shared.ai.model.Sentiment  	Companion 7com.homework.assistant.shared.ai.model.SimilarityMethod  SEMANTIC 7com.homework.assistant.shared.ai.model.SimilarityMethod  method 5com.homework.assistant.shared.ai.model.TextSimilarity  
similarity 5com.homework.assistant.shared.ai.model.TextSimilarity  text1 5com.homework.assistant.shared.ai.model.TextSimilarity  text2 5com.homework.assistant.shared.ai.model.TextSimilarity  homework *com.homework.assistant.shared.ai.model.com  	assistant 3com.homework.assistant.shared.ai.model.com.homework  shared =com.homework.assistant.shared.ai.model.com.homework.assistant  ai Dcom.homework.assistant.shared.ai.model.com.homework.assistant.shared  voice Dcom.homework.assistant.shared.ai.model.com.homework.assistant.shared  HintType Gcom.homework.assistant.shared.ai.model.com.homework.assistant.shared.ai  	VoiceInfo Jcom.homework.assistant.shared.ai.model.com.homework.assistant.shared.voice  
DailyProgress (com.homework.assistant.shared.data.model  IdGenerator (com.homework.assistant.shared.data.model  
Recitation (com.homework.assistant.shared.data.model  StudySession (com.homework.assistant.shared.data.model  StudyStatistics (com.homework.assistant.shared.data.model  StudyStatus (com.homework.assistant.shared.data.model  	StudyType (com.homework.assistant.shared.data.model  StudyTypeStats (com.homework.assistant.shared.data.model  System (com.homework.assistant.shared.data.model  Test (com.homework.assistant.shared.data.model  Word (com.homework.assistant.shared.data.model  WordCategory (com.homework.assistant.shared.data.model  assertEquals (com.homework.assistant.shared.data.model  
assertNotNull (com.homework.assistant.shared.data.model  
assertTrue (com.homework.assistant.shared.data.model  generateCategoryId (com.homework.assistant.shared.data.model  generateSessionId (com.homework.assistant.shared.data.model  generateWordId (com.homework.assistant.shared.data.model  listOf (com.homework.assistant.shared.data.model  averageScore 6com.homework.assistant.shared.data.model.DailyProgress  date 6com.homework.assistant.shared.data.model.DailyProgress  duration 6com.homework.assistant.shared.data.model.DailyProgress  sessionCount 6com.homework.assistant.shared.data.model.DailyProgress  id 3com.homework.assistant.shared.data.model.Recitation  	contentId 5com.homework.assistant.shared.data.model.StudySession  correctCount 5com.homework.assistant.shared.data.model.StudySession  	createdAt 5com.homework.assistant.shared.data.model.StudySession  duration 5com.homework.assistant.shared.data.model.StudySession  endTime 5com.homework.assistant.shared.data.model.StudySession  hints 5com.homework.assistant.shared.data.model.StudySession  id 5com.homework.assistant.shared.data.model.StudySession  mistakes 5com.homework.assistant.shared.data.model.StudySession  notes 5com.homework.assistant.shared.data.model.StudySession  score 5com.homework.assistant.shared.data.model.StudySession  	startTime 5com.homework.assistant.shared.data.model.StudySession  status 5com.homework.assistant.shared.data.model.StudySession  
totalCount 5com.homework.assistant.shared.data.model.StudySession  type 5com.homework.assistant.shared.data.model.StudySession  
DailyProgress 9com.homework.assistant.shared.data.model.StudySessionTest  IdGenerator 9com.homework.assistant.shared.data.model.StudySessionTest  StudySession 9com.homework.assistant.shared.data.model.StudySessionTest  StudyStatistics 9com.homework.assistant.shared.data.model.StudySessionTest  StudyStatus 9com.homework.assistant.shared.data.model.StudySessionTest  	StudyType 9com.homework.assistant.shared.data.model.StudySessionTest  StudyTypeStats 9com.homework.assistant.shared.data.model.StudySessionTest  System 9com.homework.assistant.shared.data.model.StudySessionTest  assertEquals 9com.homework.assistant.shared.data.model.StudySessionTest  
assertNotNull 9com.homework.assistant.shared.data.model.StudySessionTest  
assertTrue 9com.homework.assistant.shared.data.model.StudySessionTest  generateSessionId 9com.homework.assistant.shared.data.model.StudySessionTest  generateWordId 9com.homework.assistant.shared.data.model.StudySessionTest  listOf 9com.homework.assistant.shared.data.model.StudySessionTest  averageScore 8com.homework.assistant.shared.data.model.StudyStatistics  dictationStats 8com.homework.assistant.shared.data.model.StudyStatistics  recitationStats 8com.homework.assistant.shared.data.model.StudyStatistics  streak 8com.homework.assistant.shared.data.model.StudyStatistics  
totalDuration 8com.homework.assistant.shared.data.model.StudyStatistics  
totalSessions 8com.homework.assistant.shared.data.model.StudyStatistics  	COMPLETED 4com.homework.assistant.shared.data.model.StudyStatus  	Companion 4com.homework.assistant.shared.data.model.StudyStatus  IN_PROGRESS 4com.homework.assistant.shared.data.model.StudyStatus  	Companion 2com.homework.assistant.shared.data.model.StudyType  	DICTATION 2com.homework.assistant.shared.data.model.StudyType  
RECITATION 2com.homework.assistant.shared.data.model.StudyType  
totalSessions 7com.homework.assistant.shared.data.model.StudyTypeStats  category -com.homework.assistant.shared.data.model.Word  	character -com.homework.assistant.shared.data.model.Word  	createdAt -com.homework.assistant.shared.data.model.Word  
difficulty -com.homework.assistant.shared.data.model.Word  examples -com.homework.assistant.shared.data.model.Word  id -com.homework.assistant.shared.data.model.Word  meaning -com.homework.assistant.shared.data.model.Word  pinyin -com.homework.assistant.shared.data.model.Word  radicals -com.homework.assistant.shared.data.model.Word  strokeCount -com.homework.assistant.shared.data.model.Word  	updatedAt -com.homework.assistant.shared.data.model.Word  	createdAt 5com.homework.assistant.shared.data.model.WordCategory  description 5com.homework.assistant.shared.data.model.WordCategory  grade 5com.homework.assistant.shared.data.model.WordCategory  id 5com.homework.assistant.shared.data.model.WordCategory  name 5com.homework.assistant.shared.data.model.WordCategory  order 5com.homework.assistant.shared.data.model.WordCategory  semester 5com.homework.assistant.shared.data.model.WordCategory  IdGenerator 1com.homework.assistant.shared.data.model.WordTest  Word 1com.homework.assistant.shared.data.model.WordTest  WordCategory 1com.homework.assistant.shared.data.model.WordTest  assertEquals 1com.homework.assistant.shared.data.model.WordTest  
assertNotNull 1com.homework.assistant.shared.data.model.WordTest  
assertTrue 1com.homework.assistant.shared.data.model.WordTest  generateCategoryId 1com.homework.assistant.shared.data.model.WordTest  generateWordId 1com.homework.assistant.shared.data.model.WordTest  listOf 1com.homework.assistant.shared.data.model.WordTest  Boolean 'com.homework.assistant.shared.dictation  DictationProgress 'com.homework.assistant.shared.dictation  DictationResult 'com.homework.assistant.shared.dictation  DictationServiceImpl 'com.homework.assistant.shared.dictation  DictationSession 'com.homework.assistant.shared.dictation  DictationSessionState 'com.homework.assistant.shared.dictation  DictationState 'com.homework.assistant.shared.dictation  Float 'com.homework.assistant.shared.dictation  IdGenerator 'com.homework.assistant.shared.dictation  List 'com.homework.assistant.shared.dictation  String 'com.homework.assistant.shared.dictation  Test 'com.homework.assistant.shared.dictation  TextToSpeechRequest 'com.homework.assistant.shared.dictation  TextToSpeechResult 'com.homework.assistant.shared.dictation  TextToSpeechService 'com.homework.assistant.shared.dictation  Unit 'com.homework.assistant.shared.dictation  VoiceCommand 'com.homework.assistant.shared.dictation  VoiceCommandService 'com.homework.assistant.shared.dictation  VoiceCommandType 'com.homework.assistant.shared.dictation  Word 'com.homework.assistant.shared.dictation  assertEquals 'com.homework.assistant.shared.dictation  
assertNotNull 'com.homework.assistant.shared.dictation  
assertTrue 'com.homework.assistant.shared.dictation  com 'com.homework.assistant.shared.dictation  contains 'com.homework.assistant.shared.dictation  	emptyList 'com.homework.assistant.shared.dictation  first 'com.homework.assistant.shared.dictation  generateWordId 'com.homework.assistant.shared.dictation  kotlinx 'com.homework.assistant.shared.dictation  listOf 'com.homework.assistant.shared.dictation  map 'com.homework.assistant.shared.dictation  runTest 'com.homework.assistant.shared.dictation  accuracyRate 9com.homework.assistant.shared.dictation.DictationProgress  completionRate 9com.homework.assistant.shared.dictation.DictationProgress  correctCount 9com.homework.assistant.shared.dictation.DictationProgress  currentIndex 9com.homework.assistant.shared.dictation.DictationProgress  
totalCount 9com.homework.assistant.shared.dictation.DictationProgress  
wrongCount 9com.homework.assistant.shared.dictation.DictationProgress  feedback 7com.homework.assistant.shared.dictation.DictationResult  	isCorrect 7com.homework.assistant.shared.dictation.DictationResult  score 7com.homework.assistant.shared.dictation.DictationResult  DictationServiceImpl <com.homework.assistant.shared.dictation.DictationServiceTest  DictationSessionState <com.homework.assistant.shared.dictation.DictationServiceTest  IdGenerator <com.homework.assistant.shared.dictation.DictationServiceTest  TextToSpeechResult <com.homework.assistant.shared.dictation.DictationServiceTest  VoiceCommand <com.homework.assistant.shared.dictation.DictationServiceTest  VoiceCommandType <com.homework.assistant.shared.dictation.DictationServiceTest  Word <com.homework.assistant.shared.dictation.DictationServiceTest  assertEquals <com.homework.assistant.shared.dictation.DictationServiceTest  
assertNotNull <com.homework.assistant.shared.dictation.DictationServiceTest  
assertTrue <com.homework.assistant.shared.dictation.DictationServiceTest  com <com.homework.assistant.shared.dictation.DictationServiceTest  contains <com.homework.assistant.shared.dictation.DictationServiceTest  dictationService <com.homework.assistant.shared.dictation.DictationServiceTest  	emptyList <com.homework.assistant.shared.dictation.DictationServiceTest  first <com.homework.assistant.shared.dictation.DictationServiceTest  generateWordId <com.homework.assistant.shared.dictation.DictationServiceTest  kotlinx <com.homework.assistant.shared.dictation.DictationServiceTest  listOf <com.homework.assistant.shared.dictation.DictationServiceTest  map <com.homework.assistant.shared.dictation.DictationServiceTest  mockTtsService <com.homework.assistant.shared.dictation.DictationServiceTest  mockVoiceCommandService <com.homework.assistant.shared.dictation.DictationServiceTest  runTest <com.homework.assistant.shared.dictation.DictationServiceTest  currentIndex 8com.homework.assistant.shared.dictation.DictationSession  state 8com.homework.assistant.shared.dictation.DictationSession  words 8com.homework.assistant.shared.dictation.DictationSession  	Companion =com.homework.assistant.shared.dictation.DictationSessionState  	LISTENING =com.homework.assistant.shared.dictation.DictationSessionState  PAUSED =com.homework.assistant.shared.dictation.DictationSessionState  currentWord 6com.homework.assistant.shared.dictation.DictationState  progress 6com.homework.assistant.shared.dictation.DictationState  session 6com.homework.assistant.shared.dictation.DictationState  homework +com.homework.assistant.shared.dictation.com  	assistant 4com.homework.assistant.shared.dictation.com.homework  shared >com.homework.assistant.shared.dictation.com.homework.assistant  voice Ecom.homework.assistant.shared.dictation.com.homework.assistant.shared  	VoiceInfo Kcom.homework.assistant.shared.dictation.com.homework.assistant.shared.voice  DictationServiceImpl ,com.homework.assistant.shared.dictation.impl  getDictationState Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  getProgress Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  handleVoiceCommand Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  nextWord Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  pauseDictation Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  previousWord Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  resumeDictation Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  startDictation Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  submitAnswer Acom.homework.assistant.shared.dictation.impl.DictationServiceImpl  Boolean (com.homework.assistant.shared.recitation  Float (com.homework.assistant.shared.recitation  IdGenerator (com.homework.assistant.shared.recitation  Int (com.homework.assistant.shared.recitation  Intent (com.homework.assistant.shared.recitation  IntentRecognitionResult (com.homework.assistant.shared.recitation  List (com.homework.assistant.shared.recitation  
Recitation (com.homework.assistant.shared.recitation  RecitationAttempt (com.homework.assistant.shared.recitation  RecitationProgress (com.homework.assistant.shared.recitation  RecitationServiceImpl (com.homework.assistant.shared.recitation  RecitationSession (com.homework.assistant.shared.recitation  RecitationSessionState (com.homework.assistant.shared.recitation  RecitationState (com.homework.assistant.shared.recitation  SemanticAnalysisResult (com.homework.assistant.shared.recitation  SemanticAnalysisService (com.homework.assistant.shared.recitation  	Sentiment (com.homework.assistant.shared.recitation  SimilarityMethod (com.homework.assistant.shared.recitation  String (com.homework.assistant.shared.recitation  Test (com.homework.assistant.shared.recitation  TextSimilarity (com.homework.assistant.shared.recitation  TextToSpeechRequest (com.homework.assistant.shared.recitation  TextToSpeechResult (com.homework.assistant.shared.recitation  TextToSpeechService (com.homework.assistant.shared.recitation  Unit (com.homework.assistant.shared.recitation  VoiceCommand (com.homework.assistant.shared.recitation  VoiceCommandService (com.homework.assistant.shared.recitation  VoiceCommandType (com.homework.assistant.shared.recitation  assertEquals (com.homework.assistant.shared.recitation  
assertNotNull (com.homework.assistant.shared.recitation  
assertTrue (com.homework.assistant.shared.recitation  com (com.homework.assistant.shared.recitation  contains (com.homework.assistant.shared.recitation  	emptyList (com.homework.assistant.shared.recitation  first (com.homework.assistant.shared.recitation  generateRecitationId (com.homework.assistant.shared.recitation  
isNotEmpty (com.homework.assistant.shared.recitation  kotlinx (com.homework.assistant.shared.recitation  map (com.homework.assistant.shared.recitation  runTest (com.homework.assistant.shared.recitation  trim (com.homework.assistant.shared.recitation  feedback :com.homework.assistant.shared.recitation.RecitationAttempt  	isCorrect :com.homework.assistant.shared.recitation.RecitationAttempt  score :com.homework.assistant.shared.recitation.RecitationAttempt  accuracyRate ;com.homework.assistant.shared.recitation.RecitationProgress  correctAttempts ;com.homework.assistant.shared.recitation.RecitationProgress  
totalAttempts ;com.homework.assistant.shared.recitation.RecitationProgress  totalSentences ;com.homework.assistant.shared.recitation.RecitationProgress  IdGenerator >com.homework.assistant.shared.recitation.RecitationServiceTest  Intent >com.homework.assistant.shared.recitation.RecitationServiceTest  IntentRecognitionResult >com.homework.assistant.shared.recitation.RecitationServiceTest  
Recitation >com.homework.assistant.shared.recitation.RecitationServiceTest  RecitationServiceImpl >com.homework.assistant.shared.recitation.RecitationServiceTest  RecitationSessionState >com.homework.assistant.shared.recitation.RecitationServiceTest  SemanticAnalysisResult >com.homework.assistant.shared.recitation.RecitationServiceTest  	Sentiment >com.homework.assistant.shared.recitation.RecitationServiceTest  SimilarityMethod >com.homework.assistant.shared.recitation.RecitationServiceTest  TextSimilarity >com.homework.assistant.shared.recitation.RecitationServiceTest  TextToSpeechResult >com.homework.assistant.shared.recitation.RecitationServiceTest  VoiceCommand >com.homework.assistant.shared.recitation.RecitationServiceTest  VoiceCommandType >com.homework.assistant.shared.recitation.RecitationServiceTest  assertEquals >com.homework.assistant.shared.recitation.RecitationServiceTest  
assertNotNull >com.homework.assistant.shared.recitation.RecitationServiceTest  
assertTrue >com.homework.assistant.shared.recitation.RecitationServiceTest  com >com.homework.assistant.shared.recitation.RecitationServiceTest  contains >com.homework.assistant.shared.recitation.RecitationServiceTest  	emptyList >com.homework.assistant.shared.recitation.RecitationServiceTest  first >com.homework.assistant.shared.recitation.RecitationServiceTest  generateRecitationId >com.homework.assistant.shared.recitation.RecitationServiceTest  
isNotEmpty >com.homework.assistant.shared.recitation.RecitationServiceTest  kotlinx >com.homework.assistant.shared.recitation.RecitationServiceTest  map >com.homework.assistant.shared.recitation.RecitationServiceTest  mockSemanticService >com.homework.assistant.shared.recitation.RecitationServiceTest  mockTtsService >com.homework.assistant.shared.recitation.RecitationServiceTest  mockVoiceCommandService >com.homework.assistant.shared.recitation.RecitationServiceTest  recitationService >com.homework.assistant.shared.recitation.RecitationServiceTest  runTest >com.homework.assistant.shared.recitation.RecitationServiceTest  trim >com.homework.assistant.shared.recitation.RecitationServiceTest  attempts :com.homework.assistant.shared.recitation.RecitationSession  currentProgress :com.homework.assistant.shared.recitation.RecitationSession  currentSentenceIndex :com.homework.assistant.shared.recitation.RecitationSession  
recitation :com.homework.assistant.shared.recitation.RecitationSession  state :com.homework.assistant.shared.recitation.RecitationSession  	Companion ?com.homework.assistant.shared.recitation.RecitationSessionState  	LISTENING ?com.homework.assistant.shared.recitation.RecitationSessionState  PAUSED ?com.homework.assistant.shared.recitation.RecitationSessionState  currentSentence 8com.homework.assistant.shared.recitation.RecitationState  session 8com.homework.assistant.shared.recitation.RecitationState  homework ,com.homework.assistant.shared.recitation.com  	assistant 5com.homework.assistant.shared.recitation.com.homework  shared ?com.homework.assistant.shared.recitation.com.homework.assistant  ai Fcom.homework.assistant.shared.recitation.com.homework.assistant.shared  voice Fcom.homework.assistant.shared.recitation.com.homework.assistant.shared  HintType Icom.homework.assistant.shared.recitation.com.homework.assistant.shared.ai  	VoiceInfo Lcom.homework.assistant.shared.recitation.com.homework.assistant.shared.voice  RecitationServiceImpl -com.homework.assistant.shared.recitation.impl  getProgress Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  getRecitationState Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  giveHint Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  handleVoiceCommand Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  nextSentence Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  pauseRecitation Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  previousSentence Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  restartRecitation Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  resumeRecitation Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  startRecitation Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  submitRecitation Ccom.homework.assistant.shared.recitation.impl.RecitationServiceImpl  IdGenerator #com.homework.assistant.shared.utils  Test #com.homework.assistant.shared.utils  assertEquals #com.homework.assistant.shared.utils  assertNotEquals #com.homework.assistant.shared.utils  
assertTrue #com.homework.assistant.shared.utils  contains #com.homework.assistant.shared.utils  forEach #com.homework.assistant.shared.utils  generateCategoryId #com.homework.assistant.shared.utils  
generateId #com.homework.assistant.shared.utils  generateIdWithPrefix #com.homework.assistant.shared.utils  generateRecitationId #com.homework.assistant.shared.utils  generateSessionId #com.homework.assistant.shared.utils  generateWordId #com.homework.assistant.shared.utils  
startsWith #com.homework.assistant.shared.utils  generateCategoryId /com.homework.assistant.shared.utils.IdGenerator  
generateId /com.homework.assistant.shared.utils.IdGenerator  generateIdWithPrefix /com.homework.assistant.shared.utils.IdGenerator  generateRecitationId /com.homework.assistant.shared.utils.IdGenerator  generateSessionId /com.homework.assistant.shared.utils.IdGenerator  generateWordId /com.homework.assistant.shared.utils.IdGenerator  IdGenerator 3com.homework.assistant.shared.utils.IdGeneratorTest  assertEquals 3com.homework.assistant.shared.utils.IdGeneratorTest  assertNotEquals 3com.homework.assistant.shared.utils.IdGeneratorTest  
assertTrue 3com.homework.assistant.shared.utils.IdGeneratorTest  contains 3com.homework.assistant.shared.utils.IdGeneratorTest  forEach 3com.homework.assistant.shared.utils.IdGeneratorTest  generateCategoryId 3com.homework.assistant.shared.utils.IdGeneratorTest  
generateId 3com.homework.assistant.shared.utils.IdGeneratorTest  generateIdWithPrefix 3com.homework.assistant.shared.utils.IdGeneratorTest  generateRecitationId 3com.homework.assistant.shared.utils.IdGeneratorTest  generateSessionId 3com.homework.assistant.shared.utils.IdGeneratorTest  generateWordId 3com.homework.assistant.shared.utils.IdGeneratorTest  
startsWith 3com.homework.assistant.shared.utils.IdGeneratorTest  PlaybackProgress #com.homework.assistant.shared.voice  Test #com.homework.assistant.shared.voice  TextToSpeechService #com.homework.assistant.shared.voice  Unit #com.homework.assistant.shared.voice  VoiceCommandService #com.homework.assistant.shared.voice  VoiceCommandServiceImpl #com.homework.assistant.shared.voice  VoiceCommandType #com.homework.assistant.shared.voice  	VoiceInfo #com.homework.assistant.shared.voice  any #com.homework.assistant.shared.voice  assertEquals #com.homework.assistant.shared.voice  
assertTrue #com.homework.assistant.shared.voice  
component1 #com.homework.assistant.shared.voice  
component2 #com.homework.assistant.shared.voice  contains #com.homework.assistant.shared.voice  forEach #com.homework.assistant.shared.voice  forEachIndexed #com.homework.assistant.shared.voice  
isNotEmpty #com.homework.assistant.shared.voice  kotlinx #com.homework.assistant.shared.voice  listOf #com.homework.assistant.shared.voice  mapOf #com.homework.assistant.shared.voice  runTest #com.homework.assistant.shared.voice  to #com.homework.assistant.shared.voice  VoiceCommandServiceImpl ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  VoiceCommandType ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  any ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  assertEquals ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  
assertTrue ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  
component1 ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  
component2 ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  contains ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  forEachIndexed ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  
isNotEmpty ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  listOf ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  mapOf ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  runTest ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  to ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  voiceCommandService ;com.homework.assistant.shared.voice.VoiceCommandServiceTest  VoiceCommandServiceImpl (com.homework.assistant.shared.voice.impl  addCustomCommandPattern @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  getSupportedCommands @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  recognizeCommand @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  recognizeCommands @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  setConfidenceThreshold @com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl  Boolean )com.homework.assistant.shared.voice.model  Float )com.homework.assistant.shared.voice.model  IdGenerator )com.homework.assistant.shared.voice.model  Int )com.homework.assistant.shared.voice.model  Intent )com.homework.assistant.shared.voice.model  IntentRecognitionResult )com.homework.assistant.shared.voice.model  List )com.homework.assistant.shared.voice.model  
Recitation )com.homework.assistant.shared.voice.model  RecitationServiceImpl )com.homework.assistant.shared.voice.model  RecitationSessionState )com.homework.assistant.shared.voice.model  SemanticAnalysisResult )com.homework.assistant.shared.voice.model  SemanticAnalysisService )com.homework.assistant.shared.voice.model  	Sentiment )com.homework.assistant.shared.voice.model  SimilarityMethod )com.homework.assistant.shared.voice.model  String )com.homework.assistant.shared.voice.model  Test )com.homework.assistant.shared.voice.model  TextSimilarity )com.homework.assistant.shared.voice.model  TextToSpeechRequest )com.homework.assistant.shared.voice.model  TextToSpeechResult )com.homework.assistant.shared.voice.model  TextToSpeechService )com.homework.assistant.shared.voice.model  Unit )com.homework.assistant.shared.voice.model  VoiceCommand )com.homework.assistant.shared.voice.model  VoiceCommandService )com.homework.assistant.shared.voice.model  VoiceCommandType )com.homework.assistant.shared.voice.model  assertEquals )com.homework.assistant.shared.voice.model  
assertNotNull )com.homework.assistant.shared.voice.model  
assertTrue )com.homework.assistant.shared.voice.model  com )com.homework.assistant.shared.voice.model  contains )com.homework.assistant.shared.voice.model  	emptyList )com.homework.assistant.shared.voice.model  first )com.homework.assistant.shared.voice.model  generateRecitationId )com.homework.assistant.shared.voice.model  
isNotEmpty )com.homework.assistant.shared.voice.model  kotlinx )com.homework.assistant.shared.voice.model  map )com.homework.assistant.shared.voice.model  runTest )com.homework.assistant.shared.voice.model  trim )com.homework.assistant.shared.voice.model  
confidence 6com.homework.assistant.shared.voice.model.VoiceCommand  originalText 6com.homework.assistant.shared.voice.model.VoiceCommand  type 6com.homework.assistant.shared.voice.model.VoiceCommand  CANCEL :com.homework.assistant.shared.voice.model.VoiceCommandType  CONFIRM :com.homework.assistant.shared.voice.model.VoiceCommandType  	Companion :com.homework.assistant.shared.voice.model.VoiceCommandType  FINISH_RECITATION :com.homework.assistant.shared.voice.model.VoiceCommandType  	GIVE_HINT :com.homework.assistant.shared.voice.model.VoiceCommandType  HELP :com.homework.assistant.shared.voice.model.VoiceCommandType  
NEXT_SENTENCE :com.homework.assistant.shared.voice.model.VoiceCommandType  	NEXT_WORD :com.homework.assistant.shared.voice.model.VoiceCommandType  PAUSE_RECITATION :com.homework.assistant.shared.voice.model.VoiceCommandType  PREVIOUS_SENTENCE :com.homework.assistant.shared.voice.model.VoiceCommandType  
PREVIOUS_WORD :com.homework.assistant.shared.voice.model.VoiceCommandType  REPEAT_SENTENCE :com.homework.assistant.shared.voice.model.VoiceCommandType  REPEAT_WORD :com.homework.assistant.shared.voice.model.VoiceCommandType  RESTART_RECITATION :com.homework.assistant.shared.voice.model.VoiceCommandType  RESUME_RECITATION :com.homework.assistant.shared.voice.model.VoiceCommandType  SHOW_MEANING :com.homework.assistant.shared.voice.model.VoiceCommandType  
SPELL_WORD :com.homework.assistant.shared.voice.model.VoiceCommandType  START_RECITATION :com.homework.assistant.shared.voice.model.VoiceCommandType  UNKNOWN :com.homework.assistant.shared.voice.model.VoiceCommandType  homework -com.homework.assistant.shared.voice.model.com  	assistant 6com.homework.assistant.shared.voice.model.com.homework  shared @com.homework.assistant.shared.voice.model.com.homework.assistant  ai Gcom.homework.assistant.shared.voice.model.com.homework.assistant.shared  voice Gcom.homework.assistant.shared.voice.model.com.homework.assistant.shared  HintType Jcom.homework.assistant.shared.voice.model.com.homework.assistant.shared.ai  	VoiceInfo Mcom.homework.assistant.shared.voice.model.com.homework.assistant.shared.voice  currentTimeMillis java.lang.System  CharSequence kotlin  	Function1 kotlin  	Function2 kotlin  Pair kotlin  Result kotlin  map kotlin  to kotlin  not kotlin.Boolean  	compareTo kotlin.Float  	compareTo 
kotlin.Int  plus 
kotlin.Int  	compareTo kotlin.Long  minus kotlin.Long  plus kotlin.Long  	getOrNull 
kotlin.Result  	isSuccess 
kotlin.Result  contains 
kotlin.String  forEach 
kotlin.String  
isNotEmpty 
kotlin.String  length 
kotlin.String  
startsWith 
kotlin.String  to 
kotlin.String  trim 
kotlin.String  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  any kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  any kotlin.collections.List  contains kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  Entry kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  isEmpty kotlin.collections.MutableList  SuspendFunction0 kotlin.coroutines  SuspendFunction1 kotlin.coroutines  invoke "kotlin.coroutines.SuspendFunction0  
startsWith 	kotlin.io  contains 
kotlin.ranges  Sequence kotlin.sequences  any kotlin.sequences  contains kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  map kotlin.sequences  Test kotlin.test  assertEquals kotlin.test  assertNotEquals kotlin.test  
assertNotNull kotlin.test  
assertTrue kotlin.test  any kotlin.text  contains kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  
isNotEmpty kotlin.text  map kotlin.text  
startsWith kotlin.text  trim kotlin.text  CoroutineScope kotlinx.coroutines  runBlocking kotlinx.coroutines  Flow kotlinx.coroutines.flow  first kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  first kotlinx.coroutines.flow.Flow  Test 	org.junit                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           