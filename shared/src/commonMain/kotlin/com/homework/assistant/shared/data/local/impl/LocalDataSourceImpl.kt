package com.homework.assistant.shared.data.local.impl

import com.homework.assistant.shared.data.local.LocalDataSource
import com.homework.assistant.shared.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map

/**
 * 本地数据源实现
 * 这是一个内存实现，实际项目中应该使用 SQLite 或其他持久化存储
 */
class LocalDataSourceImpl : LocalDataSource {
    
    // 内存存储
    private val words = mutableMapOf<String, Word>()
    private val recitations = mutableMapOf<String, Recitation>()
    private val studySessions = mutableMapOf<String, StudySession>()
    private val settings = mutableMapOf<String, Settings>()
    
    // 状态流
    private val _wordsFlow = MutableStateFlow(words.values.toList())
    private val _recitationsFlow = MutableStateFlow(recitations.values.toList())
    private val _studySessionsFlow = MutableStateFlow(studySessions.values.toList())
    
    // Words 操作
    override suspend fun insertWord(word: Word): Result<Unit> {
        return try {
            words[word.id] = word
            _wordsFlow.value = words.values.toList()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun insertWords(wordList: List<Word>): Result<Unit> {
        return try {
            wordList.forEach { words[it.id] = it }
            _wordsFlow.value = words.values.toList()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun updateWord(word: Word): Result<Unit> {
        return try {
            if (words.containsKey(word.id)) {
                words[word.id] = word
                _wordsFlow.value = words.values.toList()
                Result.success(Unit)
            } else {
                Result.failure(Exception("Word not found: ${word.id}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun deleteWord(wordId: String): Result<Unit> {
        return try {
            words.remove(wordId)
            _wordsFlow.value = words.values.toList()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getWordById(wordId: String): Word? {
        return words[wordId]
    }
    
    override suspend fun getAllWords(): List<Word> {
        return words.values.toList()
    }
    
    override suspend fun getWordsByDifficulty(difficulty: Int): List<Word> {
        return words.values.filter { it.difficulty == difficulty }
    }
    
    override suspend fun getWordsByGrade(grade: Int): List<Word> {
        return words.values.filter { it.grade == grade }
    }
    
    override suspend fun searchWords(query: String): List<Word> {
        return words.values.filter { 
            it.character.contains(query) || 
            it.pinyin.contains(query, ignoreCase = true) ||
            it.meaning.contains(query, ignoreCase = true)
        }
    }
    
    override fun observeWords(): Flow<List<Word>> {
        return _wordsFlow
    }
    
    override fun observeWordsByDifficulty(difficulty: Int): Flow<List<Word>> {
        return _wordsFlow.map { words -> words.filter { it.difficulty == difficulty } }
    }
    
    // Recitations 操作
    override suspend fun insertRecitation(recitation: Recitation): Result<Unit> {
        return try {
            recitations[recitation.id] = recitation
            _recitationsFlow.value = recitations.values.toList()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun insertRecitations(recitationList: List<Recitation>): Result<Unit> {
        return try {
            recitationList.forEach { recitations[it.id] = it }
            _recitationsFlow.value = recitations.values.toList()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun updateRecitation(recitation: Recitation): Result<Unit> {
        return try {
            if (recitations.containsKey(recitation.id)) {
                recitations[recitation.id] = recitation
                _recitationsFlow.value = recitations.values.toList()
                Result.success(Unit)
            } else {
                Result.failure(Exception("Recitation not found: ${recitation.id}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun deleteRecitation(recitationId: String): Result<Unit> {
        return try {
            recitations.remove(recitationId)
            _recitationsFlow.value = recitations.values.toList()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getRecitationById(recitationId: String): Recitation? {
        return recitations[recitationId]
    }
    
    override suspend fun getAllRecitations(): List<Recitation> {
        return recitations.values.toList()
    }
    
    override suspend fun getRecitationsByDifficulty(difficulty: Int): List<Recitation> {
        return recitations.values.filter { it.difficulty == difficulty }
    }
    
    override suspend fun getRecitationsByGrade(grade: Int): List<Recitation> {
        return recitations.values.filter { it.grade == grade }
    }
    
    override suspend fun searchRecitations(query: String): List<Recitation> {
        return recitations.values.filter { 
            it.title.contains(query, ignoreCase = true) || 
            it.author.contains(query, ignoreCase = true) ||
            it.content.contains(query, ignoreCase = true)
        }
    }
    
    override fun observeRecitations(): Flow<List<Recitation>> {
        return _recitationsFlow
    }
    
    override fun observeRecitationsByDifficulty(difficulty: Int): Flow<List<Recitation>> {
        return _recitationsFlow.map { recitations -> recitations.filter { it.difficulty == difficulty } }
    }
    
    // StudySessions 操作
    override suspend fun insertStudySession(session: StudySession): Result<Unit> {
        return try {
            studySessions[session.id] = session
            _studySessionsFlow.value = studySessions.values.toList()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun updateStudySession(session: StudySession): Result<Unit> {
        return try {
            if (studySessions.containsKey(session.id)) {
                studySessions[session.id] = session
                _studySessionsFlow.value = studySessions.values.toList()
                Result.success(Unit)
            } else {
                Result.failure(Exception("StudySession not found: ${session.id}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun deleteStudySession(sessionId: String): Result<Unit> {
        return try {
            studySessions.remove(sessionId)
            _studySessionsFlow.value = studySessions.values.toList()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getStudySessionById(sessionId: String): StudySession? {
        return studySessions[sessionId]
    }
    
    override suspend fun getAllStudySessions(): List<StudySession> {
        return studySessions.values.toList()
    }
    
    override suspend fun getStudySessionsByType(type: StudyType): List<StudySession> {
        return studySessions.values.filter { it.type == type }
    }
    
    override suspend fun getStudySessionsByDateRange(startTime: Long, endTime: Long): List<StudySession> {
        return studySessions.values.filter { it.startTime in startTime..endTime }
    }
    
    override suspend fun getRecentStudySessions(limit: Int): List<StudySession> {
        return studySessions.values
            .sortedByDescending { it.startTime }
            .take(limit)
    }
    
    override fun observeStudySessions(): Flow<List<StudySession>> {
        return _studySessionsFlow
    }
    
    override fun observeStudySessionsByType(type: StudyType): Flow<List<StudySession>> {
        return _studySessionsFlow.map { sessions -> sessions.filter { it.type == type } }
    }
    
    // Settings 操作
    override suspend fun insertSettings(settings: Settings): Result<Unit> {
        return try {
            this.settings[settings.id] = settings
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun updateSettings(settings: Settings): Result<Unit> {
        return try {
            if (this.settings.containsKey(settings.id)) {
                this.settings[settings.id] = settings
                Result.success(Unit)
            } else {
                Result.failure(Exception("Settings not found: ${settings.id}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getSettingsById(settingsId: String): Settings? {
        return settings[settingsId]
    }
    
    override suspend fun getAllSettings(): List<Settings> {
        return settings.values.toList()
    }
    
    override suspend fun deleteAllData(): Result<Unit> {
        return try {
            words.clear()
            recitations.clear()
            studySessions.clear()
            settings.clear()
            
            _wordsFlow.value = emptyList()
            _recitationsFlow.value = emptyList()
            _studySessionsFlow.value = emptyList()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
