package com.homework.assistant.shared.data.local.impl

import com.homework.assistant.shared.data.local.LocalDataSource
import com.homework.assistant.shared.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map

/**
 * 本地数据源实现
 * 这是一个内存实现，实际项目中应该使用 SQLite 或其他持久化存储
 */
class LocalDataSourceImpl : LocalDataSource {
    
    // 内存存储
    private val words = mutableMapOf<String, Word>()
    private val wordCategories = mutableMapOf<String, WordCategory>()
    private val recitations = mutableMapOf<String, Recitation>()
    private val recitationCategories = mutableMapOf<String, RecitationCategory>()
    private val studySessions = mutableMapOf<String, StudySession>()
    
    // 状态流
    private val _wordsFlow = MutableStateFlow(words.values.toList())
    private val _wordCategoriesFlow = MutableStateFlow(wordCategories.values.toList())
    private val _recitationsFlow = MutableStateFlow(recitations.values.toList())
    private val _recitationCategoriesFlow = MutableStateFlow(recitationCategories.values.toList())
    private val _studySessionsFlow = MutableStateFlow(studySessions.values.toList())
    
    // ========== 生字相关 ==========
    
    override fun getAllWords(): Flow<List<Word>> {
        return _wordsFlow
    }
    
    override fun getWordsByCategory(categoryId: String): Flow<List<Word>> {
        return _wordsFlow.map { words -> words.filter { it.category == categoryId } }
    }
    
    override suspend fun getWordById(id: String): Word? {
        return words[id]
    }
    
    override fun searchWords(query: String): Flow<List<Word>> {
        return _wordsFlow.map { words ->
            words.filter { 
                it.character.contains(query) || 
                it.pinyin.contains(query, ignoreCase = true) ||
                it.meaning.contains(query, ignoreCase = true)
            }
        }
    }
    
    override suspend fun insertWord(word: Word) {
        words[word.id] = word
        _wordsFlow.value = words.values.toList()
    }
    
    override suspend fun updateWord(word: Word) {
        words[word.id] = word
        _wordsFlow.value = words.values.toList()
    }
    
    override suspend fun deleteWord(id: String) {
        words.remove(id)
        _wordsFlow.value = words.values.toList()
    }
    
    override suspend fun insertWords(words: List<Word>) {
        words.forEach { this.words[it.id] = it }
        _wordsFlow.value = this.words.values.toList()
    }
    
    // ========== 生字分类相关 ==========
    
    override fun getAllWordCategories(): Flow<List<WordCategory>> {
        return _wordCategoriesFlow
    }
    
    override suspend fun insertWordCategory(category: WordCategory) {
        wordCategories[category.id] = category
        _wordCategoriesFlow.value = wordCategories.values.toList()
    }
    
    override suspend fun updateWordCategory(category: WordCategory) {
        wordCategories[category.id] = category
        _wordCategoriesFlow.value = wordCategories.values.toList()
    }
    
    override suspend fun deleteWordCategory(id: String) {
        wordCategories.remove(id)
        _wordCategoriesFlow.value = wordCategories.values.toList()
    }
    
    override suspend fun getWordCountByCategory(categoryId: String): Int {
        return words.values.count { it.category == categoryId }
    }
    
    // ========== 背诵内容相关 ==========
    
    override fun getAllRecitations(): Flow<List<Recitation>> {
        return _recitationsFlow
    }
    
    override fun getRecitationsByCategory(categoryId: String): Flow<List<Recitation>> {
        return _recitationsFlow.map { recitations -> recitations.filter { it.category == categoryId } }
    }
    
    override fun getRecitationsByType(type: RecitationType): Flow<List<Recitation>> {
        // 注意：当前 Recitation 模型没有 type 字段，这里先返回所有
        return _recitationsFlow
    }
    
    override suspend fun getRecitationById(id: String): Recitation? {
        return recitations[id]
    }
    
    override fun searchRecitations(query: String): Flow<List<Recitation>> {
        return _recitationsFlow.map { recitations ->
            recitations.filter { 
                it.title.contains(query, ignoreCase = true) || 
                it.author.contains(query, ignoreCase = true) ||
                it.content.contains(query, ignoreCase = true)
            }
        }
    }
    
    override suspend fun insertRecitation(recitation: Recitation) {
        recitations[recitation.id] = recitation
        _recitationsFlow.value = recitations.values.toList()
    }
    
    override suspend fun updateRecitation(recitation: Recitation) {
        recitations[recitation.id] = recitation
        _recitationsFlow.value = recitations.values.toList()
    }
    
    override suspend fun deleteRecitation(id: String) {
        recitations.remove(id)
        _recitationsFlow.value = recitations.values.toList()
    }
    
    override suspend fun insertRecitations(recitations: List<Recitation>) {
        recitations.forEach { this.recitations[it.id] = it }
        _recitationsFlow.value = this.recitations.values.toList()
    }
    
    // ========== 背诵分类相关 ==========
    
    override fun getAllRecitationCategories(): Flow<List<RecitationCategory>> {
        return _recitationCategoriesFlow
    }
    
    override suspend fun insertRecitationCategory(category: RecitationCategory) {
        recitationCategories[category.id] = category
        _recitationCategoriesFlow.value = recitationCategories.values.toList()
    }
    
    override suspend fun updateRecitationCategory(category: RecitationCategory) {
        recitationCategories[category.id] = category
        _recitationCategoriesFlow.value = recitationCategories.values.toList()
    }
    
    override suspend fun deleteRecitationCategory(id: String) {
        recitationCategories.remove(id)
        _recitationCategoriesFlow.value = recitationCategories.values.toList()
    }
    
    override suspend fun getRecitationCountByCategory(categoryId: String): Int {
        return recitations.values.count { it.category == categoryId }
    }
    
    // ========== 学习会话相关 ==========
    
    override fun getAllStudySessions(): Flow<List<StudySession>> {
        return _studySessionsFlow
    }
    
    override fun getStudySessionsByType(type: StudyType): Flow<List<StudySession>> {
        return _studySessionsFlow.map { sessions -> sessions.filter { it.type == type } }
    }
    
    override fun getStudySessionsByContentId(contentId: String): Flow<List<StudySession>> {
        return _studySessionsFlow.map { sessions -> sessions.filter { it.contentId == contentId } }
    }
    
    override fun getStudySessionsByDateRange(startDate: Long, endDate: Long): Flow<List<StudySession>> {
        return _studySessionsFlow.map { sessions -> 
            sessions.filter { it.startTime in startDate..endDate }
        }
    }
    
    override suspend fun getStudySessionById(id: String): StudySession? {
        return studySessions[id]
    }
    
    override suspend fun insertStudySession(session: StudySession) {
        studySessions[session.id] = session
        _studySessionsFlow.value = studySessions.values.toList()
    }
    
    override suspend fun updateStudySession(session: StudySession) {
        studySessions[session.id] = session
        _studySessionsFlow.value = studySessions.values.toList()
    }
    
    override suspend fun deleteStudySession(id: String) {
        studySessions.remove(id)
        _studySessionsFlow.value = studySessions.values.toList()
    }
    
    override suspend fun cleanupOldStudySessions(beforeDate: Long): Int {
        val oldSessions = studySessions.values.filter { it.startTime < beforeDate }
        oldSessions.forEach { studySessions.remove(it.id) }
        _studySessionsFlow.value = studySessions.values.toList()
        return oldSessions.size
    }
}
