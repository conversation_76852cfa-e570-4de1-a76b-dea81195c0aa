package com.homework.assistant.shared.data.repository.impl

import com.homework.assistant.shared.data.local.LocalDataSource
import com.homework.assistant.shared.data.model.Recitation
import com.homework.assistant.shared.data.model.RecitationCategory
import com.homework.assistant.shared.data.model.RecitationType
import com.homework.assistant.shared.data.repository.RecitationRepository
import kotlinx.coroutines.flow.Flow

/**
 * 背诵仓库实现
 */
class RecitationRepositoryImpl(
    private val localDataSource: LocalDataSource
) : RecitationRepository {
    
    override suspend fun insertRecitation(recitation: Recitation): Result<Unit> {
        return try {
            localDataSource.insertRecitation(recitation)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun insertRecitations(recitations: List<Recitation>): Result<Unit> {
        return try {
            localDataSource.insertRecitations(recitations)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateRecitation(recitation: Recitation): Result<Unit> {
        return try {
            localDataSource.updateRecitation(recitation)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteRecitation(id: String): Result<Unit> {
        return try {
            localDataSource.deleteRecitation(id)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getRecitationById(id: String): Recitation? {
        return localDataSource.getRecitationById(id)
    }

    override fun getAllRecitations(): Flow<List<Recitation>> {
        return localDataSource.getAllRecitations()
    }

    override fun getRecitationsByCategory(categoryId: String): Flow<List<Recitation>> {
        return localDataSource.getRecitationsByCategory(categoryId)
    }

    override fun getRecitationsByType(type: RecitationType): Flow<List<Recitation>> {
        return localDataSource.getRecitationsByType(type)
    }

    override fun searchRecitations(query: String): Flow<List<Recitation>> {
        return localDataSource.searchRecitations(query)
    }

    override fun getAllCategories(): Flow<List<RecitationCategory>> {
        return localDataSource.getAllRecitationCategories()
    }

    override suspend fun insertCategory(category: RecitationCategory): Result<Unit> {
        return try {
            localDataSource.insertRecitationCategory(category)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateCategory(category: RecitationCategory): Result<Unit> {
        return try {
            localDataSource.updateRecitationCategory(category)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteCategory(id: String): Result<Unit> {
        return try {
            localDataSource.deleteRecitationCategory(id)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getRecitationCountByCategory(categoryId: String): Int {
        return localDataSource.getRecitationCountByCategory(categoryId)
    }

}
