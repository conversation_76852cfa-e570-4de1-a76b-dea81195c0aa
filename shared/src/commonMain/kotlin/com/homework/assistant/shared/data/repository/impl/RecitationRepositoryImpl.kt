package com.homework.assistant.shared.data.repository.impl

import com.homework.assistant.shared.data.local.LocalDataSource
import com.homework.assistant.shared.data.model.Recitation
import com.homework.assistant.shared.data.repository.RecitationRepository
import kotlinx.coroutines.flow.Flow

/**
 * 背诵仓库实现
 */
class RecitationRepositoryImpl(
    private val localDataSource: LocalDataSource
) : RecitationRepository {
    
    override suspend fun insertRecitation(recitation: Recitation): Result<Unit> {
        return localDataSource.insertRecitation(recitation)
    }
    
    override suspend fun insertRecitations(recitations: List<Recitation>): Result<Unit> {
        return localDataSource.insertRecitations(recitations)
    }
    
    override suspend fun updateRecitation(recitation: Recitation): Result<Unit> {
        return localDataSource.updateRecitation(recitation)
    }
    
    override suspend fun deleteRecitation(recitationId: String): Result<Unit> {
        return localDataSource.deleteRecitation(recitationId)
    }
    
    override suspend fun getRecitationById(recitationId: String): Recitation? {
        return localDataSource.getRecitationById(recitationId)
    }
    
    override suspend fun getAllRecitations(): List<Recitation> {
        return localDataSource.getAllRecitations()
    }
    
    override suspend fun getRecitationsByDifficulty(difficulty: Int): List<Recitation> {
        return localDataSource.getRecitationsByDifficulty(difficulty)
    }
    
    override suspend fun getRecitationsByGrade(grade: Int): List<Recitation> {
        return localDataSource.getRecitationsByGrade(grade)
    }
    
    override suspend fun searchRecitations(query: String): List<Recitation> {
        return localDataSource.searchRecitations(query)
    }
    
    override fun observeRecitations(): Flow<List<Recitation>> {
        return localDataSource.observeRecitations()
    }
    
    override fun observeRecitationsByDifficulty(difficulty: Int): Flow<List<Recitation>> {
        return localDataSource.observeRecitationsByDifficulty(difficulty)
    }
    
    override suspend fun getRandomRecitations(count: Int, difficulty: Int?): List<Recitation> {
        val allRecitations = if (difficulty != null) {
            getRecitationsByDifficulty(difficulty)
        } else {
            getAllRecitations()
        }
        
        return if (allRecitations.size <= count) {
            allRecitations
        } else {
            allRecitations.shuffled().take(count)
        }
    }
    
    override suspend fun getRecitationsForPractice(
        difficulty: Int?,
        grade: Int?,
        excludeIds: List<String>
    ): List<Recitation> {
        var recitations = getAllRecitations()
        
        if (difficulty != null) {
            recitations = recitations.filter { it.difficulty == difficulty }
        }
        
        if (grade != null) {
            recitations = recitations.filter { it.grade == grade }
        }
        
        recitations = recitations.filter { it.id !in excludeIds }
        
        return recitations
    }
    
    override suspend fun markRecitationAsLearned(recitationId: String): Result<Unit> {
        val recitation = getRecitationById(recitationId) ?: return Result.failure(Exception("Recitation not found"))
        val updatedRecitation = recitation.copy(
            lastStudiedAt = System.currentTimeMillis(),
            studyCount = recitation.studyCount + 1
        )
        return updateRecitation(updatedRecitation)
    }
    
    override suspend fun updateRecitationProgress(recitationId: String, isCorrect: Boolean): Result<Unit> {
        val recitation = getRecitationById(recitationId) ?: return Result.failure(Exception("Recitation not found"))
        val updatedRecitation = recitation.copy(
            lastStudiedAt = System.currentTimeMillis(),
            studyCount = recitation.studyCount + 1,
            correctCount = if (isCorrect) recitation.correctCount + 1 else recitation.correctCount
        )
        return updateRecitation(updatedRecitation)
    }
    
    override suspend fun getWeakRecitations(limit: Int): List<Recitation> {
        return getAllRecitations()
            .filter { it.studyCount > 0 }
            .sortedBy { 
                if (it.studyCount > 0) it.correctCount.toFloat() / it.studyCount else 0f 
            }
            .take(limit)
    }
    
    override suspend fun getRecitationsNeedingReview(): List<Recitation> {
        val now = System.currentTimeMillis()
        val oneDayAgo = now - 24 * 60 * 60 * 1000 // 24小时前
        
        return getAllRecitations()
            .filter { 
                it.lastStudiedAt > 0 && 
                it.lastStudiedAt < oneDayAgo &&
                it.studyCount > 0 &&
                (it.correctCount.toFloat() / it.studyCount) < 0.8f // 正确率低于80%
            }
    }
    
    override suspend fun getRecitationsByAuthor(author: String): List<Recitation> {
        return getAllRecitations().filter { it.author.equals(author, ignoreCase = true) }
    }
    
    override suspend fun getRecitationsByCategory(category: String): List<Recitation> {
        return getAllRecitations().filter { it.category.equals(category, ignoreCase = true) }
    }
}
