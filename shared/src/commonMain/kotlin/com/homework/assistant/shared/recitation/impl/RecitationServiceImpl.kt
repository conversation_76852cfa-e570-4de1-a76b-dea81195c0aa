package com.homework.assistant.shared.recitation.impl

import com.homework.assistant.shared.data.model.Recitation
import com.homework.assistant.shared.data.model.StudySession
import com.homework.assistant.shared.data.model.StudyType
import com.homework.assistant.shared.data.model.StudyStatus
import com.homework.assistant.shared.recitation.*
import com.homework.assistant.shared.voice.TextToSpeechService
import com.homework.assistant.shared.voice.VoiceCommandService
import com.homework.assistant.shared.voice.model.TextToSpeechRequest
import com.homework.assistant.shared.voice.model.VoiceCommandType
import com.homework.assistant.shared.ai.SemanticAnalysisService
import com.homework.assistant.shared.utils.IdGenerator
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 背诵服务实现
 */
class RecitationServiceImpl(
    private val textToSpeechService: TextToSpeechService,
    private val voiceCommandService: VoiceCommandService,
    private val semanticAnalysisService: SemanticAnalysisService
) : RecitationService {
    
    private var currentSession: RecitationSession? = null
    private val _recitationState = MutableStateFlow(
        RecitationState(
            session = null,
            currentSentence = null,
            progress = RecitationProgress(0, 0, 0, 0, 0, 0, 0, 0)
        )
    )
    
    override suspend fun startRecitation(
        recitation: Recitation,
        settings: RecitationSettings
    ): RecitationSession {
        val sentences = splitIntoSentences(recitation.content)
        val session = RecitationSession(
            id = IdGenerator.generateSessionId(),
            recitation = recitation,
            settings = settings,
            currentSentenceIndex = 0,
            state = RecitationSessionState.READY
        )
        
        currentSession = session
        updateState()
        
        // 开始播报第一句提示
        speakCurrentSentence()
        
        return session
    }
    
    override fun getRecitationState(): Flow<RecitationState> {
        return _recitationState.asStateFlow()
    }
    
    override suspend fun speakCurrentSentence(): Result<Unit> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的背诵会话"))
        val sentence = getCurrentSentence() ?: return Result.failure(Exception("没有当前句子"))
        
        session.state = RecitationSessionState.PROMPTING
        updateState()
        
        val request = TextToSpeechRequest(
            text = sentence,
            speed = session.settings.promptSpeed
        )
        
        return try {
            val result = textToSpeechService.speak(request)
            if (result.success) {
                session.state = RecitationSessionState.LISTENING
                updateState()
                Result.success(Unit)
            } else {
                Result.failure(Exception(result.error ?: "语音播放失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun repeatCurrentSentence(): Result<Unit> {
        return speakCurrentSentence()
    }
    
    override suspend fun speakFullPoem(): Result<Unit> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的背诵会话"))
        
        val request = TextToSpeechRequest(
            text = session.recitation.content,
            speed = session.settings.promptSpeed
        )
        
        return try {
            val result = textToSpeechService.speak(request)
            if (result.success) {
                Result.success(Unit)
            } else {
                Result.failure(Exception(result.error ?: "语音播放失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun giveHint(): Result<String> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的背诵会话"))
        val sentence = getCurrentSentence() ?: return Result.failure(Exception("没有当前句子"))
        
        if (!session.settings.enableHints) {
            return Result.failure(Exception("提示功能已禁用"))
        }
        
        val currentAttempts = session.attempts.count { 
            it.expectedText == sentence 
        }
        
        if (currentAttempts >= session.settings.maxHints) {
            return Result.failure(Exception("已达到最大提示次数"))
        }
        
        // 简单的提示逻辑：给出下一个字
        val progressLength = session.currentProgress.length
        val hint = if (progressLength < sentence.length) {
            "下一个字是：${sentence[progressLength]}"
        } else {
            "这句已经完成了"
        }
        
        return Result.success(hint)
    }
    
    override suspend fun nextSentence(): Result<Unit> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的背诵会话"))
        val sentences = splitIntoSentences(session.recitation.content)
        
        if (session.currentSentenceIndex < sentences.size - 1) {
            session.currentSentenceIndex++
            session.currentProgress = ""
            updateState()
            speakCurrentSentence()
            return Result.success(Unit)
        } else {
            return Result.failure(Exception("已经是最后一句"))
        }
    }
    
    override suspend fun previousSentence(): Result<Unit> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的背诵会话"))
        
        if (session.currentSentenceIndex > 0) {
            session.currentSentenceIndex--
            session.currentProgress = ""
            updateState()
            speakCurrentSentence()
            return Result.success(Unit)
        } else {
            return Result.failure(Exception("已经是第一句"))
        }
    }
    
    override suspend fun submitRecitation(userInput: String): Result<RecitationAttempt> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的背诵会话"))
        val sentence = getCurrentSentence() ?: return Result.failure(Exception("没有当前句子"))
        
        session.state = RecitationSessionState.CHECKING
        updateState()
        
        // 使用语义分析服务计算相似度
        val similarity = semanticAnalysisService.calculateSimilarity(sentence, userInput)
        val isCorrect = similarity.similarity >= session.settings.similarityThreshold
        val score = (similarity.similarity * 100).toInt()
        
        val attempt = RecitationAttempt(
            userInput = userInput,
            expectedText = sentence,
            similarity = similarity.similarity,
            isCorrect = isCorrect,
            score = score,
            timeSpent = 5000, // 简化实现
            feedback = if (isCorrect) "背诵正确！" else "需要再练习，相似度：${(similarity.similarity * 100).toInt()}%"
        )
        
        session.attempts.add(attempt)
        
        if (isCorrect) {
            session.currentProgress = sentence
            // 自动进入下一句
            if (session.settings.autoNext && session.currentSentenceIndex < splitIntoSentences(session.recitation.content).size - 1) {
                nextSentence()
            } else if (session.currentSentenceIndex >= splitIntoSentences(session.recitation.content).size - 1) {
                session.state = RecitationSessionState.COMPLETED
                updateState()
            } else {
                session.state = RecitationSessionState.LISTENING
                updateState()
            }
        } else {
            session.state = RecitationSessionState.LISTENING
            updateState()
        }
        
        return Result.success(attempt)
    }
    
    override suspend fun pauseRecitation(): Result<Unit> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的背诵会话"))
        session.state = RecitationSessionState.PAUSED
        updateState()
        return Result.success(Unit)
    }
    
    override suspend fun resumeRecitation(): Result<Unit> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的背诵会话"))
        session.state = RecitationSessionState.LISTENING
        updateState()
        return Result.success(Unit)
    }
    
    override suspend fun finishRecitation(): Result<StudySession> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的背诵会话"))
        
        session.state = RecitationSessionState.COMPLETED
        session.endTime = System.currentTimeMillis()
        
        val studySession = StudySession(
            id = session.id,
            type = StudyType.RECITATION,
            contentId = session.recitation.id,
            startTime = session.startTime,
            endTime = session.endTime,
            duration = (session.endTime ?: System.currentTimeMillis()) - session.startTime,
            status = StudyStatus.COMPLETED,
            score = calculateOverallScore(session),
            correctCount = session.attempts.count { it.isCorrect },
            totalCount = session.attempts.size
        )
        
        currentSession = null
        updateState()
        
        return Result.success(studySession)
    }
    
    override suspend fun getProgress(): RecitationProgress {
        val session = currentSession ?: return RecitationProgress(0, 0, 0, 0, 0, 0, 0, 0)
        val sentences = splitIntoSentences(session.recitation.content)
        
        val completedSentences = session.attempts.count { it.isCorrect }
        val correctAttempts = session.attempts.count { it.isCorrect }
        val totalAttempts = session.attempts.size
        val elapsedTime = System.currentTimeMillis() - session.startTime
        val averageTime = if (session.attempts.isNotEmpty()) {
            session.attempts.map { it.timeSpent }.average().toLong()
        } else 0L
        
        return RecitationProgress(
            currentSentenceIndex = session.currentSentenceIndex,
            totalSentences = sentences.size,
            completedSentences = completedSentences,
            correctAttempts = correctAttempts,
            totalAttempts = totalAttempts,
            hintsUsed = session.attempts.sumOf { it.hintsUsed },
            elapsedTime = elapsedTime,
            averageTimePerSentence = averageTime
        )
    }
    
    override suspend fun handleVoiceCommand(command: String): Result<Unit> {
        val voiceCommand = voiceCommandService.recognizeCommand(command)
        
        return when (voiceCommand.type) {
            VoiceCommandType.NEXT_SENTENCE -> nextSentence()
            VoiceCommandType.PREVIOUS_SENTENCE -> previousSentence()
            VoiceCommandType.REPEAT_SENTENCE -> repeatCurrentSentence()
            VoiceCommandType.GIVE_HINT -> {
                giveHint()
                Result.success(Unit)
            }
            VoiceCommandType.PAUSE_RECITATION -> pauseRecitation()
            VoiceCommandType.RESUME_RECITATION -> resumeRecitation()
            VoiceCommandType.FINISH_RECITATION -> {
                finishRecitation()
                Result.success(Unit)
            }
            VoiceCommandType.RESTART_RECITATION -> restartRecitation()
            else -> Result.failure(Exception("不支持的语音指令"))
        }
    }
    
    override suspend fun skipCurrentSentence(): Result<Unit> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的背诵会话"))
        
        if (!session.settings.allowSkip) {
            return Result.failure(Exception("跳过功能已禁用"))
        }
        
        return nextSentence()
    }
    
    override suspend fun restartRecitation(): Result<Unit> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的背诵会话"))
        
        session.currentSentenceIndex = 0
        session.currentProgress = ""
        session.attempts.clear()
        session.state = RecitationSessionState.READY
        
        updateState()
        speakCurrentSentence()
        
        return Result.success(Unit)
    }
    
    private fun getCurrentSentence(): String? {
        val session = currentSession ?: return null
        val sentences = splitIntoSentences(session.recitation.content)
        return if (session.currentSentenceIndex < sentences.size) {
            sentences[session.currentSentenceIndex]
        } else null
    }
    
    private suspend fun updateState() {
        val session = currentSession
        val currentSentence = getCurrentSentence()
        val progress = if (session != null) {
            getProgress()
        } else {
            RecitationProgress(0, 0, 0, 0, 0, 0, 0, 0)
        }
        
        _recitationState.value = RecitationState(
            session = session,
            currentSentence = currentSentence,
            isPrompting = session?.state == RecitationSessionState.PROMPTING,
            isListening = session?.state == RecitationSessionState.LISTENING,
            lastAttempt = session?.attempts?.lastOrNull(),
            progress = progress,
            availableCommands = getAvailableCommands()
        )
    }
    
    private fun splitIntoSentences(content: String): List<String> {
        // 简单实现：按标点符号分句
        return content.split("[，。！？；：]".toRegex())
            .map { it.trim() }
            .filter { it.isNotEmpty() }
    }
    
    private fun calculateOverallScore(session: RecitationSession): Int {
        if (session.attempts.isEmpty()) return 0
        return session.attempts.map { it.score }.average().toInt()
    }
    
    private fun getAvailableCommands(): List<String> {
        return listOf("下一句", "上一句", "重复", "提示", "暂停", "继续", "结束", "重新开始", "跳过")
    }
}
