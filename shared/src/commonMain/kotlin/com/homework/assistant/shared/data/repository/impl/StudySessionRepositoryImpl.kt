package com.homework.assistant.shared.data.repository.impl

import com.homework.assistant.shared.data.local.LocalDataSource
import com.homework.assistant.shared.data.model.StudySession
import com.homework.assistant.shared.data.model.StudyType
import com.homework.assistant.shared.data.repository.StudySessionRepository
import kotlinx.coroutines.flow.Flow

/**
 * 学习会话仓库实现
 */
class StudySessionRepositoryImpl(
    private val localDataSource: LocalDataSource
) : StudySessionRepository {
    
    override suspend fun insertStudySession(session: StudySession): Result<Unit> {
        return localDataSource.insertStudySession(session)
    }
    
    override suspend fun updateStudySession(session: StudySession): Result<Unit> {
        return localDataSource.updateStudySession(session)
    }
    
    override suspend fun deleteStudySession(sessionId: String): Result<Unit> {
        return localDataSource.deleteStudySession(sessionId)
    }
    
    override suspend fun getStudySessionById(sessionId: String): StudySession? {
        return localDataSource.getStudySessionById(sessionId)
    }
    
    override suspend fun getAllStudySessions(): List<StudySession> {
        return localDataSource.getAllStudySessions()
    }
    
    override suspend fun getStudySessionsByType(type: StudyType): List<StudySession> {
        return localDataSource.getStudySessionsByType(type)
    }
    
    override suspend fun getStudySessionsByDateRange(startTime: Long, endTime: Long): List<StudySession> {
        return localDataSource.getStudySessionsByDateRange(startTime, endTime)
    }
    
    override suspend fun getRecentStudySessions(limit: Int): List<StudySession> {
        return localDataSource.getRecentStudySessions(limit)
    }
    
    override fun observeStudySessions(): Flow<List<StudySession>> {
        return localDataSource.observeStudySessions()
    }
    
    override fun observeStudySessionsByType(type: StudyType): Flow<List<StudySession>> {
        return localDataSource.observeStudySessionsByType(type)
    }
    
    override suspend fun getTodayStudySessions(): List<StudySession> {
        val now = System.currentTimeMillis()
        val startOfDay = now - (now % (24 * 60 * 60 * 1000)) // 今天开始时间
        val endOfDay = startOfDay + (24 * 60 * 60 * 1000) - 1 // 今天结束时间
        
        return getStudySessionsByDateRange(startOfDay, endOfDay)
    }
    
    override suspend fun getWeeklyStudySessions(): List<StudySession> {
        val now = System.currentTimeMillis()
        val oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000)
        
        return getStudySessionsByDateRange(oneWeekAgo, now)
    }
    
    override suspend fun getMonthlyStudySessions(): List<StudySession> {
        val now = System.currentTimeMillis()
        val oneMonthAgo = now - (30L * 24 * 60 * 60 * 1000)
        
        return getStudySessionsByDateRange(oneMonthAgo, now)
    }
    
    override suspend fun getStudyStatistics(startTime: Long, endTime: Long): StudyStatistics {
        val sessions = getStudySessionsByDateRange(startTime, endTime)
        
        val totalSessions = sessions.size
        val totalDuration = sessions.sumOf { it.duration }
        val totalScore = sessions.map { it.score }.average().takeIf { !it.isNaN() }?.toInt() ?: 0
        val totalCorrect = sessions.sumOf { it.correctCount }
        val totalQuestions = sessions.sumOf { it.totalCount }
        
        val dictationSessions = sessions.filter { it.type == StudyType.DICTATION }
        val recitationSessions = sessions.filter { it.type == StudyType.RECITATION }
        
        return StudyStatistics(
            totalSessions = totalSessions,
            totalDuration = totalDuration,
            averageScore = totalScore,
            totalCorrect = totalCorrect,
            totalQuestions = totalQuestions,
            accuracyRate = if (totalQuestions > 0) totalCorrect.toFloat() / totalQuestions else 0f,
            dictationSessions = dictationSessions.size,
            recitationSessions = recitationSessions.size,
            averageDuration = if (totalSessions > 0) totalDuration / totalSessions else 0L
        )
    }
    
    override suspend fun getBestScores(type: StudyType?, limit: Int): List<StudySession> {
        val sessions = if (type != null) {
            getStudySessionsByType(type)
        } else {
            getAllStudySessions()
        }
        
        return sessions
            .sortedByDescending { it.score }
            .take(limit)
    }
    
    override suspend fun getStudyStreak(): Int {
        val sessions = getAllStudySessions()
            .sortedByDescending { it.startTime }
        
        if (sessions.isEmpty()) return 0
        
        var streak = 0
        var currentDate = getCurrentDateMillis()
        
        for (session in sessions) {
            val sessionDate = getDateMillis(session.startTime)
            
            if (sessionDate == currentDate) {
                streak++
                currentDate -= 24 * 60 * 60 * 1000 // 前一天
            } else if (sessionDate == currentDate - 24 * 60 * 60 * 1000) {
                streak++
                currentDate = sessionDate - 24 * 60 * 60 * 1000
            } else {
                break
            }
        }
        
        return streak
    }
    
    private fun getCurrentDateMillis(): Long {
        val now = System.currentTimeMillis()
        return now - (now % (24 * 60 * 60 * 1000))
    }
    
    private fun getDateMillis(timestamp: Long): Long {
        return timestamp - (timestamp % (24 * 60 * 60 * 1000))
    }
}

/**
 * 学习统计数据
 */
data class StudyStatistics(
    val totalSessions: Int,
    val totalDuration: Long,
    val averageScore: Int,
    val totalCorrect: Int,
    val totalQuestions: Int,
    val accuracyRate: Float,
    val dictationSessions: Int,
    val recitationSessions: Int,
    val averageDuration: Long
)
