package com.homework.assistant.shared.ai.impl

import com.homework.assistant.shared.ai.SemanticAnalysisService
import com.homework.assistant.shared.ai.HintType
import com.homework.assistant.shared.ai.model.*

/**
 * 语义分析服务Mock实现
 * 这是一个简单的Mock实现，用于测试业务逻辑
 * 实际生产环境应该集成 LLM 服务（如 OpenAI、Claude 等）
 */
class SemanticAnalysisServiceImpl : SemanticAnalysisService {

    override suspend fun analyzeSemantics(
        text: String,
        context: String?
    ): SemanticAnalysisResult {
        // Mock实现：返回固定的分析结果
        return SemanticAnalysisResult(
            originalText = text,
            normalizedText = text.trim(),
            sentiment = Sentiment.POSITIVE, // Mock: 总是返回积极情感
            entities = emptyList(),
            keywords = listOf("学习", "生字", "背诵"), // Mock: 固定关键词
            confidence = 0.9f,
            processingTime = 50L
        )
    }
    
    override suspend fun calculateSimilarity(
        text1: String,
        text2: String
    ): TextSimilarity {
        // Mock实现：简单的相似度计算
        val similarity = when {
            text1 == text2 -> 1.0f
            text1.isEmpty() || text2.isEmpty() -> 0.0f
            text1.contains(text2) || text2.contains(text1) -> 0.8f
            else -> 0.3f // Mock: 不同文本返回低相似度
        }

        return TextSimilarity(
            text1 = text1,
            text2 = text2,
            similarity = similarity,
            method = SimilarityMethod.SEMANTIC,
            details = SimilarityDetails(
                commonWords = emptyList(),
                uniqueWords1 = emptyList(),
                uniqueWords2 = emptyList()
            )
        )
    }
    
    override suspend fun recognizeIntent(
        text: String,
        domain: String
    ): IntentRecognitionResult {
        val intent = when {
            text.contains("开始") && text.contains("听写") -> Intent.START_DICTATION
            text.contains("开始") && text.contains("背诵") -> Intent.START_RECITATION
            text.contains("提示") || text.contains("帮我") -> Intent.REQUEST_HINT
            text.contains("帮助") || text.contains("怎么") || text.contains("如何") -> Intent.REQUEST_HELP
            text.contains("确认") || text.contains("好的") || text.contains("是的") -> Intent.CONFIRM_ACTION
            text.contains("取消") || text.contains("不要") || text.contains("停止") -> Intent.CANCEL_ACTION
            text.contains("？") || text.contains("什么") || text.contains("为什么") -> Intent.ASK_QUESTION
            else -> Intent.UNKNOWN
        }

        val confidence = if (intent != Intent.UNKNOWN) 0.8f else 0.1f

        return IntentRecognitionResult(
            intent = intent,
            confidence = confidence
        )
    }
    
    override suspend fun extractKeywords(text: String, maxKeywords: Int): List<String> {
        // Mock实现：返回固定关键词
        return listOf("学习", "生字", "背诵").take(maxKeywords)
    }

    override suspend fun correctText(text: String, context: String?): String {
        // Mock实现：不做任何纠错
        return text
    }

    override suspend fun isRecitationComplete(
        originalText: String,
        recitedText: String,
        tolerance: Float
    ): Boolean {
        // Mock实现：简单的完整性判断，用于测试业务逻辑
        return recitedText.trim().length >= (originalText.trim().length * tolerance).toInt()
    }

    override suspend fun generateRecitationHint(
        originalText: String,
        currentProgress: String,
        hintType: HintType
    ): String {
        // Mock实现：返回固定提示
        return when (hintType) {
            HintType.NEXT_WORD -> "下一个词是..."
            HintType.NEXT_SENTENCE -> "下一句是..."
            HintType.MEANING -> "这段话的意思是..."
            HintType.CONTEXT -> "联系上下文想想..."
            HintType.RHYME -> "注意押韵..."
        }
    }
}
