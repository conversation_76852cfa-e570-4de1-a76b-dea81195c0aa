package com.homework.assistant.shared.ai.impl

import com.homework.assistant.shared.ai.SemanticAnalysisService
import com.homework.assistant.shared.ai.HintType
import com.homework.assistant.shared.ai.model.*

/**
 * 语义分析服务简单实现
 * 这是一个基础实现，主要用于测试和演示
 * 实际生产环境应该集成 LLM 服务（如 OpenAI、Claude 等）
 */
class SemanticAnalysisServiceImpl : SemanticAnalysisService {
    
    override suspend fun analyzeSemantics(
        text: String,
        context: String?
    ): SemanticAnalysisResult {
        // 基于关键词的简单情感分析
        val sentiment = analyzeSentiment(text)
        val keywords = extractKeywordsFromText(text)

        return SemanticAnalysisResult(
            originalText = text,
            normalizedText = text.trim(),
            sentiment = sentiment,
            entities = emptyList(),
            keywords = keywords,
            confidence = 0.8f,
            processingTime = 100L
        )
    }

    private fun analyzeSentiment(text: String): Sentiment {
        val positiveWords = listOf("喜欢", "好", "棒", "优秀", "开心", "高兴", "满意", "成功", "完美", "赞")
        val negativeWords = listOf("不喜欢", "坏", "差", "失败", "难过", "生气", "讨厌", "错误", "糟糕", "失望")

        val positiveCount = positiveWords.count { text.contains(it) }
        val negativeCount = negativeWords.count { text.contains(it) }

        return when {
            positiveCount > negativeCount -> Sentiment.POSITIVE
            negativeCount > positiveCount -> Sentiment.NEGATIVE
            else -> Sentiment.NEUTRAL
        }
    }

    private fun extractKeywordsFromText(text: String): List<String> {
        return text.split("").filter { it.isNotBlank() && it.length > 1 }.take(3)
    }
    
    override suspend fun calculateSimilarity(
        text1: String,
        text2: String
    ): TextSimilarity {
        val similarity = calculateTextSimilarity(text1, text2)
        val words1 = text1.split("").filter { it.isNotBlank() }
        val words2 = text2.split("").filter { it.isNotBlank() }
        val commonWords = words1.intersect(words2.toSet()).toList()
        val uniqueWords1 = words1.subtract(words2.toSet()).toList()
        val uniqueWords2 = words2.subtract(words1.toSet()).toList()

        return TextSimilarity(
            text1 = text1,
            text2 = text2,
            similarity = similarity,
            method = SimilarityMethod.SEMANTIC,
            details = SimilarityDetails(
                commonWords = commonWords,
                uniqueWords1 = uniqueWords1,
                uniqueWords2 = uniqueWords2
            )
        )
    }

    private fun calculateTextSimilarity(text1: String, text2: String): Float {
        if (text1 == text2) return 1.0f
        if (text1.isEmpty() || text2.isEmpty()) return 0.0f

        val chars1 = text1.toSet()
        val chars2 = text2.toSet()
        val intersection = chars1.intersect(chars2).size
        val union = chars1.union(chars2).size

        return if (union == 0) 0.0f else intersection.toFloat() / union.toFloat()
    }
    
    override suspend fun recognizeIntent(
        text: String,
        domain: String
    ): IntentRecognitionResult {
        // 简单实现，实际应该使用 LLM 进行意图识别
        val intent = when {
            text.contains("开始") && text.contains("听写") -> Intent.START_DICTATION
            text.contains("开始") && text.contains("背诵") -> Intent.START_RECITATION
            text.contains("提示") -> Intent.REQUEST_HINT
            text.contains("帮助") -> Intent.REQUEST_HELP
            text.contains("确认") -> Intent.CONFIRM_ACTION
            text.contains("取消") -> Intent.CANCEL_ACTION
            text.contains("？") -> Intent.ASK_QUESTION
            else -> Intent.UNKNOWN
        }

        return IntentRecognitionResult(
            intent = intent,
            confidence = 0.8f
        )
    }
    
    override suspend fun extractKeywords(text: String, maxKeywords: Int): List<String> {
        // 简单实现，实际应该使用 LLM 提取关键词
        return text.split(" ").filter { it.isNotBlank() }.take(maxKeywords)
    }

    override suspend fun correctText(text: String, context: String?): String {
        // 简单实现，实际应该使用 LLM 进行文本纠错
        return text
    }

    override suspend fun isRecitationComplete(
        originalText: String,
        recitedText: String,
        tolerance: Float
    ): Boolean {
        // 简单实现，实际应该使用 LLM 进行语义比较
        return originalText.trim() == recitedText.trim()
    }

    override suspend fun generateRecitationHint(
        originalText: String,
        currentProgress: String,
        hintType: HintType
    ): String {
        // 简单实现，实际应该使用 LLM 生成智能提示
        return when (hintType) {
            HintType.NEXT_WORD -> "下一个词是..."
            HintType.NEXT_SENTENCE -> "下一句是..."
            HintType.MEANING -> "这段话的意思是..."
            HintType.CONTEXT -> "联系上下文想想..."
            HintType.RHYME -> "注意押韵..."
        }
    }
}
