package com.homework.assistant.shared.data.repository.impl

import com.homework.assistant.shared.data.local.LocalDataSource
import com.homework.assistant.shared.data.model.Word
import com.homework.assistant.shared.data.model.WordCategory
import com.homework.assistant.shared.data.repository.WordRepository
import kotlinx.coroutines.flow.Flow

/**
 * 生字仓库实现
 */
class WordRepositoryImpl(
    private val localDataSource: LocalDataSource
) : WordRepository {
    
    override suspend fun insertWord(word: Word): Result<Unit> {
        return try {
            localDataSource.insertWord(word)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun insertWords(words: List<Word>): Result<Unit> {
        return try {
            localDataSource.insertWords(words)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateWord(word: Word): Result<Unit> {
        return try {
            localDataSource.updateWord(word)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteWord(id: String): Result<Unit> {
        return try {
            localDataSource.deleteWord(id)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getWordById(id: String): Word? {
        return localDataSource.getWordById(id)
    }

    override fun getAllWords(): Flow<List<Word>> {
        return localDataSource.getAllWords()
    }

    override fun getWordsByCategory(categoryId: String): Flow<List<Word>> {
        return localDataSource.getWordsByCategory(categoryId)
    }

    override fun searchWords(query: String): Flow<List<Word>> {
        return localDataSource.searchWords(query)
    }

    override fun getAllCategories(): Flow<List<WordCategory>> {
        return localDataSource.getAllWordCategories()
    }

    override suspend fun insertCategory(category: WordCategory): Result<Unit> {
        return try {
            localDataSource.insertWordCategory(category)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateCategory(category: WordCategory): Result<Unit> {
        return try {
            localDataSource.updateWordCategory(category)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteCategory(id: String): Result<Unit> {
        return try {
            localDataSource.deleteWordCategory(id)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getWordCountByCategory(categoryId: String): Int {
        return localDataSource.getWordCountByCategory(categoryId)
    }

}
