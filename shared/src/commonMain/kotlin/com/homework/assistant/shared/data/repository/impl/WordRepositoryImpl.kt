package com.homework.assistant.shared.data.repository.impl

import com.homework.assistant.shared.data.local.LocalDataSource
import com.homework.assistant.shared.data.model.Word
import com.homework.assistant.shared.data.repository.WordRepository
import kotlinx.coroutines.flow.Flow

/**
 * 生字仓库实现
 */
class WordRepositoryImpl(
    private val localDataSource: LocalDataSource
) : WordRepository {
    
    override suspend fun insertWord(word: Word): Result<Unit> {
        return localDataSource.insertWord(word)
    }
    
    override suspend fun insertWords(words: List<Word>): Result<Unit> {
        return localDataSource.insertWords(words)
    }
    
    override suspend fun updateWord(word: Word): Result<Unit> {
        return localDataSource.updateWord(word)
    }
    
    override suspend fun deleteWord(wordId: String): Result<Unit> {
        return localDataSource.deleteWord(wordId)
    }
    
    override suspend fun getWordById(wordId: String): Word? {
        return localDataSource.getWordById(wordId)
    }
    
    override suspend fun getAllWords(): List<Word> {
        return localDataSource.getAllWords()
    }
    
    override suspend fun getWordsByDifficulty(difficulty: Int): List<Word> {
        return localDataSource.getWordsByDifficulty(difficulty)
    }
    
    override suspend fun getWordsByGrade(grade: Int): List<Word> {
        return localDataSource.getWordsByGrade(grade)
    }
    
    override suspend fun searchWords(query: String): List<Word> {
        return localDataSource.searchWords(query)
    }
    
    override fun observeWords(): Flow<List<Word>> {
        return localDataSource.observeWords()
    }
    
    override fun observeWordsByDifficulty(difficulty: Int): Flow<List<Word>> {
        return localDataSource.observeWordsByDifficulty(difficulty)
    }
    
    override suspend fun getRandomWords(count: Int, difficulty: Int?): List<Word> {
        val allWords = if (difficulty != null) {
            getWordsByDifficulty(difficulty)
        } else {
            getAllWords()
        }
        
        return if (allWords.size <= count) {
            allWords
        } else {
            allWords.shuffled().take(count)
        }
    }
    
    override suspend fun getWordsForPractice(
        difficulty: Int?,
        grade: Int?,
        excludeIds: List<String>
    ): List<Word> {
        var words = getAllWords()
        
        if (difficulty != null) {
            words = words.filter { it.difficulty == difficulty }
        }
        
        if (grade != null) {
            words = words.filter { it.grade == grade }
        }
        
        words = words.filter { it.id !in excludeIds }
        
        return words
    }
    
    override suspend fun markWordAsLearned(wordId: String): Result<Unit> {
        val word = getWordById(wordId) ?: return Result.failure(Exception("Word not found"))
        val updatedWord = word.copy(
            lastStudiedAt = System.currentTimeMillis(),
            studyCount = word.studyCount + 1
        )
        return updateWord(updatedWord)
    }
    
    override suspend fun updateWordProgress(wordId: String, isCorrect: Boolean): Result<Unit> {
        val word = getWordById(wordId) ?: return Result.failure(Exception("Word not found"))
        val updatedWord = word.copy(
            lastStudiedAt = System.currentTimeMillis(),
            studyCount = word.studyCount + 1,
            correctCount = if (isCorrect) word.correctCount + 1 else word.correctCount
        )
        return updateWord(updatedWord)
    }
    
    override suspend fun getWeakWords(limit: Int): List<Word> {
        return getAllWords()
            .filter { it.studyCount > 0 }
            .sortedBy { 
                if (it.studyCount > 0) it.correctCount.toFloat() / it.studyCount else 0f 
            }
            .take(limit)
    }
    
    override suspend fun getWordsNeedingReview(): List<Word> {
        val now = System.currentTimeMillis()
        val oneDayAgo = now - 24 * 60 * 60 * 1000 // 24小时前
        
        return getAllWords()
            .filter { 
                it.lastStudiedAt > 0 && 
                it.lastStudiedAt < oneDayAgo &&
                it.studyCount > 0 &&
                (it.correctCount.toFloat() / it.studyCount) < 0.8f // 正确率低于80%
            }
    }
}
