package com.homework.assistant.shared.dictation

import com.homework.assistant.shared.data.model.Word
import com.homework.assistant.shared.dictation.impl.DictationServiceImpl
import com.homework.assistant.shared.dictation.DictationSessionState
import com.homework.assistant.shared.voice.TextToSpeechService
import com.homework.assistant.shared.voice.VoiceCommandService
import com.homework.assistant.shared.voice.model.TextToSpeechRequest
import com.homework.assistant.shared.voice.model.TextToSpeechResult
import com.homework.assistant.shared.voice.model.VoiceCommand
import com.homework.assistant.shared.voice.model.VoiceCommandType
import com.homework.assistant.shared.utils.IdGenerator
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.test.assertNotNull

/**
 * 听写服务测试
 */
class DictationServiceTest {
    
    // Mock 实现
    private val mockTtsService = object : TextToSpeechService {
        override suspend fun speak(request: TextToSpeechRequest): TextToSpeechResult {
            return TextToSpeechResult(success = true, duration = 1000)
        }
        
        override suspend fun speak(text: String, speed: Float): TextToSpeechResult {
            return TextToSpeechResult(success = true, duration = 1000)
        }
        
        override suspend fun stop() {}
        override suspend fun pause() {}
        override suspend fun resume() {}
        override fun isSpeaking(): Boolean = false
        override suspend fun getAvailableVoices() = emptyList<com.homework.assistant.shared.voice.VoiceInfo>()
        override suspend fun setDefaultVoice(voiceId: String) {}
        override fun getPlaybackProgress() = kotlinx.coroutines.flow.flowOf(
            com.homework.assistant.shared.voice.PlaybackProgress(0, 0, false, "")
        )
    }
    
    private val mockVoiceCommandService = object : VoiceCommandService {
        override suspend fun recognizeCommand(text: String): VoiceCommand {
            val type = when {
                text.contains("下一个") -> VoiceCommandType.NEXT_WORD
                text.contains("上一个") -> VoiceCommandType.PREVIOUS_WORD
                text.contains("重复") -> VoiceCommandType.REPEAT_WORD
                text.contains("拼音") -> VoiceCommandType.SPELL_WORD
                text.contains("含义") -> VoiceCommandType.SHOW_MEANING
                else -> VoiceCommandType.UNKNOWN
            }
            return VoiceCommand(type, text, 0.8f)
        }
        
        override suspend fun recognizeCommands(texts: List<String>) = texts.map { recognizeCommand(it) }
        override fun addCustomCommandPattern(pattern: String, commandType: String) {}
        override fun getSupportedCommands() = emptyList<String>()
        override fun setConfidenceThreshold(threshold: Float) {}
    }
    
    private val dictationService = DictationServiceImpl(mockTtsService, mockVoiceCommandService)
    
    @Test
    fun `开始听写应该创建会话并播报第一个生字`() = runTest {
        // Arrange
        val words = listOf(
            Word(
                id = IdGenerator.generateWordId(),
                character = "学",
                pinyin = "xué",
                meaning = "学习"
            ),
            Word(
                id = IdGenerator.generateWordId(),
                character = "习",
                pinyin = "xí",
                meaning = "练习"
            )
        )
        
        // Act
        val session = dictationService.startDictation(words)
        val state = dictationService.getDictationState().first()
        
        // Assert
        assertNotNull(session)
        assertEquals(2, session.words.size)
        assertEquals(0, session.currentIndex)
        assertEquals(DictationSessionState.LISTENING, session.state)
        assertEquals("学", state.currentWord?.character)
    }
    
    @Test
    fun `提交正确答案应该记录结果并进入下一个生字`() = runTest {
        // Arrange
        val words = listOf(
            Word(
                id = IdGenerator.generateWordId(),
                character = "学",
                pinyin = "xué",
                meaning = "学习"
            ),
            Word(
                id = IdGenerator.generateWordId(),
                character = "习",
                pinyin = "xí",
                meaning = "练习"
            )
        )
        
        dictationService.startDictation(words)
        
        // Act
        val result = dictationService.submitAnswer("学")
        val state = dictationService.getDictationState().first()
        
        // Assert
        assertTrue(result.isSuccess)
        val dictationResult = result.getOrNull()
        assertNotNull(dictationResult)
        assertTrue(dictationResult.isCorrect)
        assertEquals(100, dictationResult.score)
        assertEquals("习", state.currentWord?.character) // 应该进入下一个生字
    }
    
    @Test
    fun `提交错误答案应该记录错误结果`() = runTest {
        // Arrange
        val words = listOf(
            Word(
                id = IdGenerator.generateWordId(),
                character = "学",
                pinyin = "xué",
                meaning = "学习"
            )
        )
        
        dictationService.startDictation(words)
        
        // Act
        val result = dictationService.submitAnswer("错误答案")
        
        // Assert
        assertTrue(result.isSuccess)
        val dictationResult = result.getOrNull()
        assertNotNull(dictationResult)
        assertTrue(!dictationResult.isCorrect)
        assertEquals(0, dictationResult.score)
        assertTrue(dictationResult.feedback.contains("错误"))
    }
    
    @Test
    fun `下一个生字指令应该切换到下一个生字`() = runTest {
        // Arrange
        val words = listOf(
            Word(
                id = IdGenerator.generateWordId(),
                character = "学",
                pinyin = "xué",
                meaning = "学习"
            ),
            Word(
                id = IdGenerator.generateWordId(),
                character = "习",
                pinyin = "xí",
                meaning = "练习"
            )
        )
        
        dictationService.startDictation(words)
        
        // Act
        val result = dictationService.nextWord()
        val state = dictationService.getDictationState().first()
        
        // Assert
        assertTrue(result.isSuccess)
        assertEquals("习", state.currentWord?.character)
        assertEquals(1, state.progress.currentIndex)
    }
    
    @Test
    fun `上一个生字指令应该切换到上一个生字`() = runTest {
        // Arrange
        val words = listOf(
            Word(
                id = IdGenerator.generateWordId(),
                character = "学",
                pinyin = "xué",
                meaning = "学习"
            ),
            Word(
                id = IdGenerator.generateWordId(),
                character = "习",
                pinyin = "xí",
                meaning = "练习"
            )
        )
        
        dictationService.startDictation(words)
        dictationService.nextWord() // 先到第二个
        
        // Act
        val result = dictationService.previousWord()
        val state = dictationService.getDictationState().first()
        
        // Assert
        assertTrue(result.isSuccess)
        assertEquals("学", state.currentWord?.character)
        assertEquals(0, state.progress.currentIndex)
    }
    
    @Test
    fun `处理语音指令应该执行对应操作`() = runTest {
        // Arrange
        val words = listOf(
            Word(
                id = IdGenerator.generateWordId(),
                character = "学",
                pinyin = "xué",
                meaning = "学习"
            ),
            Word(
                id = IdGenerator.generateWordId(),
                character = "习",
                pinyin = "xí",
                meaning = "练习"
            )
        )
        
        dictationService.startDictation(words)
        
        // Act
        val result = dictationService.handleVoiceCommand("下一个")
        val state = dictationService.getDictationState().first()
        
        // Assert
        assertTrue(result.isSuccess)
        assertEquals("习", state.currentWord?.character)
    }
    
    @Test
    fun `获取进度应该返回正确的统计信息`() {
        // Arrange
        val words = listOf(
            Word(
                id = IdGenerator.generateWordId(),
                character = "学",
                pinyin = "xué",
                meaning = "学习"
            ),
            Word(
                id = IdGenerator.generateWordId(),
                character = "习",
                pinyin = "xí",
                meaning = "练习"
            )
        )

        runBlocking {
            // 设置不自动跳转到下一个词
            val settings = DictationSettings(autoNext = false)
            dictationService.startDictation(words, settings)
            dictationService.submitAnswer("学") // 正确答案

            // Act
            val progress = dictationService.getProgress()

            // Debug: 打印实际值
            println("Debug - currentIndex: ${progress.currentIndex}")
            println("Debug - totalCount: ${progress.totalCount}")
            println("Debug - correctCount: ${progress.correctCount}")
            println("Debug - wrongCount: ${progress.wrongCount}")
            println("Debug - completionRate: ${progress.completionRate}")
            println("Debug - accuracyRate: ${progress.accuracyRate}")

            // Assert
            assertEquals(0, progress.currentIndex) // 没有自动跳转，还在第一个词
            assertEquals(2, progress.totalCount)
            assertEquals(1, progress.correctCount)
            assertEquals(0, progress.wrongCount)
            assertEquals(0.5f, progress.completionRate) // (0+1)/2 = 0.5
            assertEquals(1.0f, progress.accuracyRate) // 1/1 = 1.0
        }
    }
    
    @Test
    fun `暂停和恢复听写应该正确更新状态`() = runTest {
        // Arrange
        val words = listOf(
            Word(
                id = IdGenerator.generateWordId(),
                character = "学",
                pinyin = "xué",
                meaning = "学习"
            )
        )
        
        dictationService.startDictation(words)
        
        // Act & Assert - 暂停
        val pauseResult = dictationService.pauseDictation()
        assertTrue(pauseResult.isSuccess)
        
        val pausedState = dictationService.getDictationState().first()
        assertEquals(DictationSessionState.PAUSED, pausedState.session?.state)
        
        // Act & Assert - 恢复
        val resumeResult = dictationService.resumeDictation()
        assertTrue(resumeResult.isSuccess)
        
        val resumedState = dictationService.getDictationState().first()
        assertEquals(DictationSessionState.LISTENING, resumedState.session?.state)
    }
}

// 简单的测试运行器
private fun runTest(block: suspend () -> Unit) {
    kotlinx.coroutines.runBlocking {
        block()
    }
}
