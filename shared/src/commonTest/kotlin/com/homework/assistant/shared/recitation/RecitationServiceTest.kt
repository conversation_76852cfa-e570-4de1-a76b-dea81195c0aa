package com.homework.assistant.shared.recitation

import com.homework.assistant.shared.data.model.Recitation
import com.homework.assistant.shared.recitation.impl.RecitationServiceImpl
import com.homework.assistant.shared.voice.TextToSpeechService
import com.homework.assistant.shared.voice.VoiceCommandService
import com.homework.assistant.shared.voice.model.*
import com.homework.assistant.shared.ai.SemanticAnalysisService
import com.homework.assistant.shared.ai.model.*
import com.homework.assistant.shared.utils.IdGenerator
import kotlinx.coroutines.flow.first
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.test.assertNotNull

/**
 * 背诵服务测试
 */
class RecitationServiceTest {
    
    // Mock 实现
    private val mockTtsService = object : TextToSpeechService {
        override suspend fun speak(request: TextToSpeechRequest): TextToSpeechResult {
            return TextToSpeechResult(success = true, duration = 1000)
        }
        
        override suspend fun speak(text: String, speed: Float): TextToSpeechResult {
            return TextToSpeechResult(success = true, duration = 1000)
        }
        
        override suspend fun stop() {}
        override suspend fun pause() {}
        override suspend fun resume() {}
        override fun isSpeaking(): Boolean = false
        override suspend fun getAvailableVoices() = emptyList<com.homework.assistant.shared.voice.VoiceInfo>()
        override suspend fun setDefaultVoice(voiceId: String) {}
        override fun getPlaybackProgress() = kotlinx.coroutines.flow.flowOf(
            com.homework.assistant.shared.voice.PlaybackProgress(0, 0, false, "")
        )
    }
    
    private val mockVoiceCommandService = object : VoiceCommandService {
        override suspend fun recognizeCommand(text: String): VoiceCommand {
            val type = when {
                text.contains("下一句") -> VoiceCommandType.NEXT_SENTENCE
                text.contains("上一句") -> VoiceCommandType.PREVIOUS_SENTENCE
                text.contains("重复") -> VoiceCommandType.REPEAT_SENTENCE
                text.contains("提示") -> VoiceCommandType.GIVE_HINT
                text.contains("暂停") -> VoiceCommandType.PAUSE_RECITATION
                text.contains("继续") -> VoiceCommandType.RESUME_RECITATION
                text.contains("重新开始") -> VoiceCommandType.RESTART_RECITATION
                else -> VoiceCommandType.UNKNOWN
            }
            return VoiceCommand(type, text, 0.8f)
        }
        
        override suspend fun recognizeCommands(texts: List<String>) = texts.map { recognizeCommand(it) }
        override fun addCustomCommandPattern(pattern: String, commandType: String) {}
        override fun getSupportedCommands() = emptyList<String>()
        override fun setConfidenceThreshold(threshold: Float) {}
    }
    
    private val mockSemanticService = object : SemanticAnalysisService {
        override suspend fun analyzeSemantics(text: String, context: String?) = SemanticAnalysisResult(
            originalText = text,
            normalizedText = text,
            sentiment = Sentiment.NEUTRAL,
            entities = emptyList(),
            keywords = emptyList(),
            confidence = 0.8f
        )
        
        override suspend fun calculateSimilarity(text1: String, text2: String): TextSimilarity {
            // 简单的相似度计算：完全匹配为1.0，否则为0.8
            val similarity = if (text1.trim() == text2.trim()) 1.0f else 0.8f
            return TextSimilarity(text1, text2, similarity, SimilarityMethod.SEMANTIC)
        }
        
        override suspend fun recognizeIntent(text: String, domain: String) = IntentRecognitionResult(
            Intent.UNKNOWN, 0.5f
        )
        
        override suspend fun extractKeywords(text: String, maxKeywords: Int) = emptyList<String>()
        override suspend fun correctText(text: String, context: String?) = text
        override suspend fun isRecitationComplete(originalText: String, recitedText: String, tolerance: Float) = true
        override suspend fun generateRecitationHint(originalText: String, currentProgress: String, hintType: com.homework.assistant.shared.ai.HintType) = "提示"
    }
    
    private val recitationService = RecitationServiceImpl(
        mockTtsService, 
        mockVoiceCommandService, 
        mockSemanticService
    )
    
    @Test
    fun `开始背诵应该创建会话并播报第一句`() = runTest {
        // Arrange
        val recitation = Recitation(
            id = IdGenerator.generateRecitationId(),
            title = "静夜思",
            author = "李白",
            content = "床前明月光，疑是地上霜。举头望明月，低头思故乡。",
            difficulty = 1
        )
        
        // Act
        val session = recitationService.startRecitation(recitation)
        val state = recitationService.getRecitationState().first()
        
        // Assert
        assertNotNull(session)
        assertEquals(recitation.id, session.recitation.id)
        assertEquals(0, session.currentSentenceIndex)
        assertEquals(RecitationSessionState.LISTENING, session.state)
        assertNotNull(state.currentSentence)
        assertTrue(state.currentSentence!!.isNotEmpty())
    }
    
    @Test
    fun `提交正确背诵应该记录结果并进入下一句`() = runTest {
        // Arrange
        val recitation = Recitation(
            id = IdGenerator.generateRecitationId(),
            title = "静夜思",
            author = "李白",
            content = "床前明月光，疑是地上霜。",
            difficulty = 1
        )
        
        recitationService.startRecitation(recitation)
        val state = recitationService.getRecitationState().first()
        val firstSentence = state.currentSentence!!
        
        // Act
        val result = recitationService.submitRecitation(firstSentence)
        val newState = recitationService.getRecitationState().first()
        
        // Assert
        assertTrue(result.isSuccess)
        val attempt = result.getOrNull()
        assertNotNull(attempt)
        assertTrue(attempt.isCorrect)
        assertEquals(100, attempt.score)
        // 应该进入下一句（如果有的话）
        assertTrue(newState.session!!.currentSentenceIndex >= 0)
    }
    
    @Test
    fun `提交错误背诵应该记录错误结果`() = runTest {
        // Arrange
        val recitation = Recitation(
            id = IdGenerator.generateRecitationId(),
            title = "静夜思",
            author = "李白",
            content = "床前明月光，疑是地上霜。",
            difficulty = 1
        )
        
        recitationService.startRecitation(recitation)
        
        // Act
        val result = recitationService.submitRecitation("错误的背诵内容")
        
        // Assert
        assertTrue(result.isSuccess)
        val attempt = result.getOrNull()
        assertNotNull(attempt)
        assertTrue(!attempt.isCorrect)
        assertEquals(80, attempt.score) // Mock 返回 0.8 相似度
        assertTrue(attempt.feedback.contains("需要再练习"))
    }
    
    @Test
    fun `下一句指令应该切换到下一句`() = runTest {
        // Arrange
        val recitation = Recitation(
            id = IdGenerator.generateRecitationId(),
            title = "静夜思",
            author = "李白",
            content = "床前明月光，疑是地上霜。举头望明月，低头思故乡。",
            difficulty = 1
        )
        
        recitationService.startRecitation(recitation)
        val initialState = recitationService.getRecitationState().first()
        val initialSentence = initialState.currentSentence
        
        // Act
        val result = recitationService.nextSentence()
        val newState = recitationService.getRecitationState().first()
        
        // Assert
        assertTrue(result.isSuccess)
        assertEquals(1, newState.session!!.currentSentenceIndex)
        assertTrue(newState.currentSentence != initialSentence)
    }
    
    @Test
    fun `上一句指令应该切换到上一句`() = runTest {
        // Arrange
        val recitation = Recitation(
            id = IdGenerator.generateRecitationId(),
            title = "静夜思",
            author = "李白",
            content = "床前明月光，疑是地上霜。举头望明月，低头思故乡。",
            difficulty = 1
        )
        
        recitationService.startRecitation(recitation)
        recitationService.nextSentence() // 先到第二句
        val secondSentence = recitationService.getRecitationState().first().currentSentence
        
        // Act
        val result = recitationService.previousSentence()
        val newState = recitationService.getRecitationState().first()
        
        // Assert
        assertTrue(result.isSuccess)
        assertEquals(0, newState.session!!.currentSentenceIndex)
        assertTrue(newState.currentSentence != secondSentence)
    }
    
    @Test
    fun `给出提示应该返回提示信息`() = runTest {
        // Arrange
        val recitation = Recitation(
            id = IdGenerator.generateRecitationId(),
            title = "静夜思",
            author = "李白",
            content = "床前明月光，疑是地上霜。",
            difficulty = 1
        )
        
        recitationService.startRecitation(recitation)
        
        // Act
        val result = recitationService.giveHint()
        
        // Assert
        assertTrue(result.isSuccess)
        val hint = result.getOrNull()
        assertNotNull(hint)
        assertTrue(hint.contains("下一个字是") || hint.contains("完成"))
    }
    
    @Test
    fun `处理语音指令应该执行对应操作`() = runTest {
        // Arrange
        val recitation = Recitation(
            id = IdGenerator.generateRecitationId(),
            title = "静夜思",
            author = "李白",
            content = "床前明月光，疑是地上霜。举头望明月，低头思故乡。",
            difficulty = 1
        )
        
        recitationService.startRecitation(recitation)
        val initialIndex = recitationService.getRecitationState().first().session!!.currentSentenceIndex
        
        // Act
        val result = recitationService.handleVoiceCommand("下一句")
        val newState = recitationService.getRecitationState().first()
        
        // Assert
        assertTrue(result.isSuccess)
        assertEquals(initialIndex + 1, newState.session!!.currentSentenceIndex)
    }
    
    @Test
    fun `获取进度应该返回正确的统计信息`() = runTest {
        // Arrange
        val recitation = Recitation(
            id = IdGenerator.generateRecitationId(),
            title = "静夜思",
            author = "李白",
            content = "床前明月光，疑是地上霜。举头望明月，低头思故乡。",
            difficulty = 1
        )
        
        recitationService.startRecitation(recitation)
        val state = recitationService.getRecitationState().first()
        val firstSentence = state.currentSentence!!
        recitationService.submitRecitation(firstSentence) // 正确背诵第一句
        
        // Act
        val progress = recitationService.getProgress()
        
        // Assert
        assertTrue(progress.totalSentences > 0)
        assertEquals(1, progress.correctAttempts)
        assertEquals(1, progress.totalAttempts)
        assertTrue(progress.accuracyRate > 0.0f)
    }
    
    @Test
    fun `暂停和恢复背诵应该正确更新状态`() = runTest {
        // Arrange
        val recitation = Recitation(
            id = IdGenerator.generateRecitationId(),
            title = "静夜思",
            author = "李白",
            content = "床前明月光，疑是地上霜。",
            difficulty = 1
        )
        
        recitationService.startRecitation(recitation)
        
        // Act & Assert - 暂停
        val pauseResult = recitationService.pauseRecitation()
        assertTrue(pauseResult.isSuccess)
        
        val pausedState = recitationService.getRecitationState().first()
        assertEquals(RecitationSessionState.PAUSED, pausedState.session?.state)
        
        // Act & Assert - 恢复
        val resumeResult = recitationService.resumeRecitation()
        assertTrue(resumeResult.isSuccess)
        
        val resumedState = recitationService.getRecitationState().first()
        assertEquals(RecitationSessionState.LISTENING, resumedState.session?.state)
    }
    
    @Test
    fun `重新开始背诵应该重置状态`() = runTest {
        // Arrange
        val recitation = Recitation(
            id = IdGenerator.generateRecitationId(),
            title = "静夜思",
            author = "李白",
            content = "床前明月光，疑是地上霜。举头望明月，低头思故乡。",
            difficulty = 1
        )
        
        recitationService.startRecitation(recitation)
        recitationService.nextSentence() // 移动到第二句
        
        // Act
        val result = recitationService.restartRecitation()
        val state = recitationService.getRecitationState().first()
        
        // Assert
        assertTrue(result.isSuccess)
        assertEquals(0, state.session!!.currentSentenceIndex)
        assertEquals("", state.session!!.currentProgress)
        assertTrue(state.session!!.attempts.isEmpty())
    }
}

// 简单的测试运行器
private fun runTest(block: suspend () -> Unit) {
    kotlinx.coroutines.runBlocking {
        block()
    }
}
