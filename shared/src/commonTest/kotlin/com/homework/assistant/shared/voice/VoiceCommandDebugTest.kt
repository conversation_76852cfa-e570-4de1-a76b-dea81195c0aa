package com.homework.assistant.shared.voice

import com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl
import com.homework.assistant.shared.voice.model.VoiceCommandType
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

/**
 * 语音指令调试测试
 */
class VoiceCommandDebugTest {
    
    private val voiceCommandService = VoiceCommandServiceImpl()
    
    @Test
    fun `调试语音指令识别`() {
        val testTexts = listOf("下一个", "帮助", "随便说")

        testTexts.forEach { text ->
            val command = runBlocking { voiceCommandService.recognizeCommand(text) }
            println("输入: '$text' -> 类型: ${command.type}, 置信度: ${command.confidence}")
        }
    }
}
