package com.homework.assistant.shared.voice

import com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl
import com.homework.assistant.shared.voice.model.VoiceCommandType
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * 语音指令识别服务测试
 */
class VoiceCommandServiceTest {
    
    private val voiceCommandService = VoiceCommandServiceImpl()
    
    @Test
    fun `识别下一个词指令`() {
        // Arrange
        val testTexts = listOf("下一个", "下个词", "下一题", "下一个词")

        // Act & Assert
        testTexts.forEach { text ->
            val command = runBlocking { voiceCommandService.recognizeCommand(text) }
            assertEquals(VoiceCommandType.NEXT_WORD, command.type)
            assertEquals(text, command.originalText)
            assertTrue(command.confidence > 0.0f)
        }
    }

    @Test
    fun `识别通用指令`() {
        // Arrange
        val testCases = mapOf(
            "帮助" to VoiceCommandType.HELP,
            "取消" to VoiceCommandType.CANCEL,
            "确认" to VoiceCommandType.CONFIRM
        )

        // Act & Assert
        testCases.forEach { (text, expectedType) ->
            val command = runBlocking { voiceCommandService.recognizeCommand(text) }
            assertEquals(expectedType, command.type)
            assertTrue(command.confidence > 0.0f)
        }
    }

    @Test
    fun `识别未知指令`() {
        // Arrange
        val unknownTexts = listOf("随便说点什么", "今天天气怎么样", "我想吃饭")

        // Act & Assert
        unknownTexts.forEach { text ->
            val command = runBlocking { voiceCommandService.recognizeCommand(text) }
            assertEquals(VoiceCommandType.UNKNOWN, command.type)
            assertEquals(0.0f, command.confidence)
        }
    }
}
