package com.homework.assistant.shared.data.repository

import com.homework.assistant.shared.data.local.impl.LocalDataSourceImpl
import com.homework.assistant.shared.data.model.Word
import com.homework.assistant.shared.data.repository.impl.WordRepositoryImpl
import com.homework.assistant.shared.utils.IdGenerator
import kotlinx.coroutines.flow.first
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.test.assertNotNull
import kotlin.test.assertNull

/**
 * 生字仓库测试
 */
class WordRepositoryTest {
    
    private val localDataSource = LocalDataSourceImpl()
    private val wordRepository = WordRepositoryImpl(localDataSource)
    
    @Test
    fun `插入生字应该成功`() = runTest {
        // Arrange
        val word = Word(
            id = IdGenerator.generateWordId(),
            character = "学",
            pinyin = "xué",
            meaning = "学习",
            difficulty = 1,
            grade = 1
        )
        
        // Act
        val result = wordRepository.insertWord(word)
        
        // Assert
        assertTrue(result.isSuccess)
        val retrievedWord = wordRepository.getWordById(word.id)
        assertNotNull(retrievedWord)
        assertEquals(word.character, retrievedWord.character)
    }
    
    @Test
    fun `批量插入生字应该成功`() = runTest {
        // Arrange
        val words = listOf(
            Word(
                id = IdGenerator.generateWordId(),
                character = "学",
                pinyin = "xué",
                meaning = "学习",
                difficulty = 1,
                grade = 1
            ),
            Word(
                id = IdGenerator.generateWordId(),
                character = "习",
                pinyin = "xí",
                meaning = "练习",
                difficulty = 1,
                grade = 1
            )
        )
        
        // Act
        val result = wordRepository.insertWords(words)
        
        // Assert
        assertTrue(result.isSuccess)
        val allWords = wordRepository.getAllWords()
        assertEquals(2, allWords.size)
    }
    
    @Test
    fun `更新生字应该成功`() = runTest {
        // Arrange
        val word = Word(
            id = IdGenerator.generateWordId(),
            character = "学",
            pinyin = "xué",
            meaning = "学习",
            difficulty = 1,
            grade = 1
        )
        wordRepository.insertWord(word)
        
        val updatedWord = word.copy(meaning = "学习知识")
        
        // Act
        val result = wordRepository.updateWord(updatedWord)
        
        // Assert
        assertTrue(result.isSuccess)
        val retrievedWord = wordRepository.getWordById(word.id)
        assertNotNull(retrievedWord)
        assertEquals("学习知识", retrievedWord.meaning)
    }
    
    @Test
    fun `删除生字应该成功`() = runTest {
        // Arrange
        val word = Word(
            id = IdGenerator.generateWordId(),
            character = "学",
            pinyin = "xué",
            meaning = "学习",
            difficulty = 1,
            grade = 1
        )
        wordRepository.insertWord(word)
        
        // Act
        val result = wordRepository.deleteWord(word.id)
        
        // Assert
        assertTrue(result.isSuccess)
        val retrievedWord = wordRepository.getWordById(word.id)
        assertNull(retrievedWord)
    }
    
    @Test
    fun `按难度查询生字应该返回正确结果`() = runTest {
        // Arrange
        val words = listOf(
            Word(
                id = IdGenerator.generateWordId(),
                character = "学",
                pinyin = "xué",
                meaning = "学习",
                difficulty = 1,
                grade = 1
            ),
            Word(
                id = IdGenerator.generateWordId(),
                character = "习",
                pinyin = "xí",
                meaning = "练习",
                difficulty = 2,
                grade = 1
            )
        )
        wordRepository.insertWords(words)
        
        // Act
        val difficulty1Words = wordRepository.getWordsByDifficulty(1)
        val difficulty2Words = wordRepository.getWordsByDifficulty(2)
        
        // Assert
        assertEquals(1, difficulty1Words.size)
        assertEquals("学", difficulty1Words[0].character)
        assertEquals(1, difficulty2Words.size)
        assertEquals("习", difficulty2Words[0].character)
    }
    
    @Test
    fun `搜索生字应该返回匹配结果`() = runTest {
        // Arrange
        val words = listOf(
            Word(
                id = IdGenerator.generateWordId(),
                character = "学",
                pinyin = "xué",
                meaning = "学习",
                difficulty = 1,
                grade = 1
            ),
            Word(
                id = IdGenerator.generateWordId(),
                character = "习",
                pinyin = "xí",
                meaning = "练习",
                difficulty = 1,
                grade = 1
            )
        )
        wordRepository.insertWords(words)
        
        // Act
        val searchResults = wordRepository.searchWords("学")
        
        // Assert
        assertEquals(2, searchResults.size) // "学" 和 "学习" 都匹配
    }
    
    @Test
    fun `获取随机生字应该返回指定数量`() = runTest {
        // Arrange
        val words = (1..10).map { i ->
            Word(
                id = IdGenerator.generateWordId(),
                character = "字$i",
                pinyin = "zi$i",
                meaning = "意思$i",
                difficulty = 1,
                grade = 1
            )
        }
        wordRepository.insertWords(words)
        
        // Act
        val randomWords = wordRepository.getRandomWords(5)
        
        // Assert
        assertEquals(5, randomWords.size)
    }
    
    @Test
    fun `标记生字为已学习应该更新学习状态`() = runTest {
        // Arrange
        val word = Word(
            id = IdGenerator.generateWordId(),
            character = "学",
            pinyin = "xué",
            meaning = "学习",
            difficulty = 1,
            grade = 1
        )
        wordRepository.insertWord(word)
        
        // Act
        val result = wordRepository.markWordAsLearned(word.id)
        
        // Assert
        assertTrue(result.isSuccess)
        val updatedWord = wordRepository.getWordById(word.id)
        assertNotNull(updatedWord)
        assertEquals(1, updatedWord.studyCount)
        assertTrue(updatedWord.lastStudiedAt > 0)
    }
    
    @Test
    fun `更新生字进度应该正确记录`() = runTest {
        // Arrange
        val word = Word(
            id = IdGenerator.generateWordId(),
            character = "学",
            pinyin = "xué",
            meaning = "学习",
            difficulty = 1,
            grade = 1
        )
        wordRepository.insertWord(word)
        
        // Act
        wordRepository.updateWordProgress(word.id, true) // 正确
        wordRepository.updateWordProgress(word.id, false) // 错误
        
        // Assert
        val updatedWord = wordRepository.getWordById(word.id)
        assertNotNull(updatedWord)
        assertEquals(2, updatedWord.studyCount)
        assertEquals(1, updatedWord.correctCount)
    }
    
    @Test
    fun `获取薄弱生字应该按正确率排序`() = runTest {
        // Arrange
        val word1 = Word(
            id = IdGenerator.generateWordId(),
            character = "学",
            pinyin = "xué",
            meaning = "学习",
            difficulty = 1,
            grade = 1,
            studyCount = 10,
            correctCount = 2 // 20% 正确率
        )
        val word2 = Word(
            id = IdGenerator.generateWordId(),
            character = "习",
            pinyin = "xí",
            meaning = "练习",
            difficulty = 1,
            grade = 1,
            studyCount = 10,
            correctCount = 8 // 80% 正确率
        )
        wordRepository.insertWords(listOf(word1, word2))
        
        // Act
        val weakWords = wordRepository.getWeakWords(5)
        
        // Assert
        assertTrue(weakWords.isNotEmpty())
        assertEquals("学", weakWords[0].character) // 正确率更低的排在前面
    }
    
    @Test
    fun `观察生字流应该响应数据变化`() = runTest {
        // Arrange
        val word = Word(
            id = IdGenerator.generateWordId(),
            character = "学",
            pinyin = "xué",
            meaning = "学习",
            difficulty = 1,
            grade = 1
        )
        
        // Act
        val initialWords = wordRepository.observeWords().first()
        wordRepository.insertWord(word)
        val updatedWords = wordRepository.observeWords().first()
        
        // Assert
        assertEquals(0, initialWords.size)
        assertEquals(1, updatedWords.size)
        assertEquals("学", updatedWords[0].character)
    }
}

// 简单的测试运行器
private fun runTest(block: suspend () -> Unit) {
    kotlinx.coroutines.runBlocking {
        block()
    }
}
